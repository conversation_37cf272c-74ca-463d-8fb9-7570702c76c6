"use client";

import { Award, BookOpen, Calendar, Users } from "lucide-react";
import { AnimateIn } from "@/components/ui/animations";
import {
  ModernCard,
  ModernCardContent,
  StatCard,
} from "@/components/ui/modern-card";

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface InstructorDashboardProps {
  user: User;
}

export default function InstructorDashboard({
  user: _user,
}: InstructorDashboardProps) {
  return (
    <div className="space-y-8">
      {/* Estadísticas del instructor */}
      <AnimateIn animation="fade" duration="normal">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Mis Cursos"
            value={0}
            change="Cursos asignados"
            trend="neutral"
            icon={BookOpen}
            color="blue"
          />
          <StatCard
            title="Estudiantes"
            value={0}
            change="Total de alumnos"
            trend="neutral"
            icon={Users}
            color="green"
          />
          <StatCard
            title="Clases Programadas"
            value={0}
            change="Esta semana"
            trend="neutral"
            icon={Calendar}
            color="purple"
          />
          <StatCard
            title="Certificados Emitidos"
            value={0}
            change="Este mes"
            trend="neutral"
            icon={Award}
            color="orange"
          />
        </div>
      </AnimateIn>

      {/* Panel en desarrollo */}
      <AnimateIn animation="slide" direction="up" delay={200}>
        <ModernCard>
          <ModernCardContent>
            <div className="text-center py-12">
              <BookOpen className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-foreground mb-4">
                Panel de Instructor
              </h2>
              <p className="text-muted-foreground max-w-md mx-auto">
                El panel de instructor está en desarrollo. Pronto podrás
                gestionar tus cursos, ver el progreso de tus estudiantes y
                administrar las clases.
              </p>
              <div className="mt-8 space-y-2">
                <h3 className="text-lg font-semibold text-foreground">
                  Funcionalidades próximamente:
                </h3>
                <ul className="text-muted-foreground space-y-1">
                  <li>• Gestión de cursos asignados</li>
                  <li>• Seguimiento del progreso de estudiantes</li>
                  <li>• Programación de clases</li>
                  <li>• Emisión de certificados</li>
                  <li>• Reportes de rendimiento</li>
                </ul>
              </div>
            </div>
          </ModernCardContent>
        </ModernCard>
      </AnimateIn>
    </div>
  );
}
