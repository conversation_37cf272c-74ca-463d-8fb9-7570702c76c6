"use client";

import { Award, Download, Eye, Search } from "lucide-react";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { AnimateIn, StaggeredList } from "@/components/ui/animations";
import { PageError } from "@/components/ui/error-boundary";
import { LoadingPage } from "@/components/ui/loading";
import { ModernButton } from "@/components/ui/modern-button";
import {
  ModernCard,
  ModernCardContent,
} from "@/components/ui/modern-card";
import { supabase } from "@/lib/supabase";

// Define types
type Certificate = {
  id: string;
  certificate_number?: string;
  issue_date: string;
  expiry_date: string | null;
  status: string;
  qr_code_url: string | null;
  course_id?: string;
  course?: {
    title: string;
    description: string | null;
  };
};

export default function StudentCertificates() {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [filteredCertificates, setFilteredCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const fetchCertificates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error("Usuario no autenticado");
      }

      // Fetch certificates with course information
      const { data: certificatesData, error: certificatesError } = await supabase
        .from("certificates")
        .select(`
          id,
          certificate_number,
          issue_date,
          expiry_date,
          status,
          qr_code_url,
          course_id,
          courses (
            title,
            description
          )
        `)
        .eq("user_id", user.id)
        .order("issue_date", { ascending: false });

      if (certificatesError) {
        throw certificatesError;
      }

      setCertificates(certificatesData || []);
      setFilteredCertificates(certificatesData || []);
    } catch (error) {
      console.error("Error fetching certificates:", error);
      setError(error instanceof Error ? error.message : "Error desconocido");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCertificates();
  }, [fetchCertificates]);

  // Filter certificates based on search term and status
  useEffect(() => {
    let filtered = certificates;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(cert =>
        cert.course?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.certificate_number?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter(cert => cert.status?.toLowerCase() === statusFilter);
    }

    setFilteredCertificates(filtered);
  }, [certificates, searchTerm, statusFilter]);

  if (loading) {
    return <LoadingPage />;
  }

  if (error) {
    return <PageError message={error} onRetry={fetchCertificates} />;
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "expired":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "revoked":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "Activo";
      case "expired":
        return "Expirado";
      case "revoked":
        return "Revocado";
      default:
        return status || "Desconocido";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-border pb-4">
        <h1 className="text-3xl font-bold text-foreground">Mis Certificados</h1>
        <p className="text-muted-foreground mt-1">
          Gestiona y descarga tus certificados obtenidos
        </p>
      </div>

      {/* Filters */}
      <AnimateIn animation="fade" duration="normal">
        <ModernCard>
          <ModernCardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Buscar por curso o número de certificado..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="sm:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="all">Todos los estados</option>
                  <option value="active">Activos</option>
                  <option value="expired">Expirados</option>
                  <option value="revoked">Revocados</option>
                </select>
              </div>
            </div>
          </ModernCardContent>
        </ModernCard>
      </AnimateIn>

      {/* Certificates List */}
      <AnimateIn animation="slide" direction="up" delay={200}>
        {filteredCertificates.length > 0 ? (
          <StaggeredList className="space-y-4">
            {filteredCertificates.map((certificate) => (
              <ModernCard key={certificate.id}>
                <ModernCardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 flex-1 min-w-0">
                      <div className="bg-primary/10 p-3 rounded-lg">
                        <Award className="w-6 h-6 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-foreground truncate">
                          {certificate.course?.title || "Curso no especificado"}
                        </h3>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-sm text-muted-foreground">
                            #{certificate.certificate_number || "Sin número"}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            Emitido: {new Date(certificate.issue_date).toLocaleDateString()}
                          </span>
                          {certificate.expiry_date && (
                            <span className="text-sm text-muted-foreground">
                              Expira: {new Date(certificate.expiry_date).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                        {certificate.course?.description && (
                          <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                            {certificate.course.description}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <span
                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                          certificate.status
                        )}`}
                      >
                        {getStatusLabel(certificate.status)}
                      </span>
                      
                      <Link href={`/panel/mis-certificados/${certificate.id}`}>
                        <ModernButton variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-2" />
                          Ver
                        </ModernButton>
                      </Link>
                      
                      {certificate.qr_code_url && (
                        <ModernButton variant="outline" size="sm">
                          <Download className="w-4 h-4 mr-2" />
                          Descargar
                        </ModernButton>
                      )}
                    </div>
                  </div>
                </ModernCardContent>
              </ModernCard>
            ))}
          </StaggeredList>
        ) : (
          <ModernCard>
            <ModernCardContent>
              <div className="text-center py-12">
                <Award className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">
                  {searchTerm || statusFilter !== "all" 
                    ? "No se encontraron certificados" 
                    : "No tienes certificados aún"}
                </h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || statusFilter !== "all"
                    ? "Intenta ajustar los filtros de búsqueda"
                    : "Completa un curso para obtener tu primer certificado"}
                </p>
                {(!searchTerm && statusFilter === "all") && (
                  <Link href="/cursos">
                    <ModernButton>
                      Explorar Cursos
                    </ModernButton>
                  </Link>
                )}
              </div>
            </ModernCardContent>
          </ModernCard>
        )}
      </AnimateIn>
    </div>
  );
}
