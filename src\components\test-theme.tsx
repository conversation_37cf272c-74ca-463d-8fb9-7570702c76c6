"use client";

import { useEffect, useState } from "react";
import { useThemeContext } from "@/components/theme/theme-provider";
import { useThemeFix } from "@/hooks/use-theme-fix";

export function TestTheme() {
  const { theme, systemTheme, resolvedTheme } = useThemeContext();
  useThemeFix(); // Apply theme fix
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 bg-card border rounded-lg p-4 text-sm shadow-lg z-50 max-w-xs">
      <h3 className="font-bold mb-2">Theme Status</h3>
      <div className="space-y-1 text-xs">
        <div>
          Theme: <code className="bg-muted px-1 rounded">{theme}</code>
        </div>
        <div>
          System: <code className="bg-muted px-1 rounded">{systemTheme}</code>
        </div>
        <div>
          Resolved:{" "}
          <code className="bg-muted px-1 rounded">{resolvedTheme}</code>
        </div>
        <div>
          HTML class:{" "}
          <code className="bg-muted px-1 rounded">
            {document.documentElement.className}
          </code>
        </div>
        <div className="mt-2 p-2 bg-background border rounded">
          <div className="text-foreground">Foreground text</div>
          <div className="text-muted-foreground">Muted text</div>
        </div>
      </div>
    </div>
  );
}
