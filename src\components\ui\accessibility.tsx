/**
 * @fileoverview Accessibility Components and Utilities (WCAG 2.1 AA)
 *
 * Componentes y utilidades para mejorar la accesibilidad:
 * - Skip links
 * - Screen reader utilities
 * - Focus management
 * - Keyboard navigation
 * - ARIA helpers
 */

"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

// ============================================================================
// Accessibility Hooks
// ============================================================================

/**
 * Hook para gestión de foco
 */
export function useFocusManagement() {
  const focusableElementsSelector =
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';

  const trapFocus = React.useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      focusableElementsSelector,
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[
      focusableElements.length - 1
    ] as HTMLElement;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Tab") {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    container.addEventListener("keydown", handleKeyDown);
    return () => container.removeEventListener("keydown", handleKeyDown);
  }, []);

  const restoreFocus = React.useCallback(
    (previousElement: HTMLElement | null) => {
      if (previousElement) {
        previousElement.focus();
      }
    },
    [],
  );

  return { trapFocus, restoreFocus };
}

/**
 * Hook para detección de preferencias de usuario
 */
export function useAccessibilityPreferences() {
  const [preferences, setPreferences] = React.useState({
    prefersReducedMotion: false,
    prefersHighContrast: false,
    prefersDarkMode: false,
  });

  React.useEffect(() => {
    const updatePreferences = () => {
      setPreferences({
        prefersReducedMotion: window.matchMedia(
          "(prefers-reduced-motion: reduce)",
        ).matches,
        prefersHighContrast: window.matchMedia("(prefers-contrast: high)")
          .matches,
        prefersDarkMode: window.matchMedia("(prefers-color-scheme: dark)")
          .matches,
      });
    };

    updatePreferences();

    const mediaQueries = [
      window.matchMedia("(prefers-reduced-motion: reduce)"),
      window.matchMedia("(prefers-contrast: high)"),
      window.matchMedia("(prefers-color-scheme: dark)"),
    ];

    mediaQueries.forEach((mq) =>
      mq.addEventListener("change", updatePreferences),
    );

    return () => {
      mediaQueries.forEach((mq) =>
        mq.removeEventListener("change", updatePreferences),
      );
    };
  }, []);

  return preferences;
}

// ============================================================================
// Accessibility Components
// ============================================================================

/**
 * Skip Link para navegación por teclado
 */
interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

export function SkipLink({ href, children, className }: SkipLinkProps) {
  return (
    <a
      href={href}
      className={cn(
        "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50",
        "bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium",
        "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        className,
      )}
    >
      {children}
    </a>
  );
}

/**
 * Texto solo para lectores de pantalla
 */
interface ScreenReaderOnlyProps {
  children: React.ReactNode;
  className?: string;
}

export function ScreenReaderOnly({
  children,
  className,
}: ScreenReaderOnlyProps) {
  return <span className={cn("sr-only", className)}>{children}</span>;
}

/**
 * Indicador de estado para lectores de pantalla
 */
interface LiveRegionProps {
  children: React.ReactNode;
  politeness?: "polite" | "assertive" | "off";
  atomic?: boolean;
  className?: string;
}

export function LiveRegion({
  children,
  politeness = "polite",
  atomic = false,
  className,
}: LiveRegionProps) {
  return (
    <div
      aria-live={politeness}
      aria-atomic={atomic}
      className={cn("sr-only", className)}
    >
      {children}
    </div>
  );
}

/**
 * Botón con estados de accesibilidad mejorados
 */
interface AccessibleButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: "primary" | "secondary" | "ghost" | "destructive";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
  loadingText?: string;
  describedBy?: string;
}

export const AccessibleButton = React.forwardRef<
  HTMLButtonElement,
  AccessibleButtonProps
>(
  (
    {
      children,
      variant = "primary",
      size = "md",
      loading = false,
      loadingText = "Cargando...",
      describedBy,
      className,
      ...props
    },
    ref,
  ) => {
    const buttonId = React.useId();

    return (
      <>
        <button
          ref={ref}
          id={buttonId}
          aria-describedby={describedBy}
          aria-busy={loading}
          disabled={loading || props.disabled}
          className={cn(
            "inline-flex items-center justify-center rounded-md font-medium transition-colors",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            "disabled:pointer-events-none disabled:opacity-50",
            // Variants
            variant === "primary" &&
              "bg-primary text-primary-foreground hover:bg-primary/90",
            variant === "secondary" &&
              "bg-secondary text-secondary-foreground hover:bg-secondary/80",
            variant === "ghost" &&
              "hover:bg-accent hover:text-accent-foreground",
            variant === "destructive" &&
              "bg-destructive text-destructive-foreground hover:bg-destructive/90",
            // Sizes
            size === "sm" && "h-9 px-3 text-sm",
            size === "md" && "h-10 px-4 py-2",
            size === "lg" && "h-11 px-8 text-lg",
            className,
          )}
          {...props}
        >
          {loading ? loadingText : children}
        </button>

        {loading && <LiveRegion>{loadingText}</LiveRegion>}
      </>
    );
  },
);
AccessibleButton.displayName = "AccessibleButton";

/**
 * Modal con gestión de foco
 */
interface AccessibleModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export function AccessibleModal({
  isOpen,
  onClose,
  title,
  description,
  children,
  className,
}: AccessibleModalProps) {
  const modalRef = React.useRef<HTMLDivElement>(null);
  const previousActiveElement = React.useRef<HTMLElement | null>(null);
  const { trapFocus, restoreFocus } = useFocusManagement();

  React.useEffect(() => {
    if (isOpen) {
      previousActiveElement.current = document.activeElement as HTMLElement;

      const cleanup = modalRef.current
        ? trapFocus(modalRef.current)
        : undefined;

      // Focus en el modal
      setTimeout(() => {
        modalRef.current?.focus();
      }, 100);

      return cleanup;
    } else {
      restoreFocus(previousActiveElement.current);
    }
  }, [isOpen, trapFocus, restoreFocus]);

  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <div
        ref={modalRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        aria-describedby={description ? "modal-description" : undefined}
        tabIndex={-1}
        className={cn(
          "bg-background rounded-lg shadow-lg max-w-md w-full p-6",
          "focus:outline-none focus:ring-2 focus:ring-ring",
          className,
        )}
      >
        <h2 id="modal-title" className="text-lg font-semibold mb-2">
          {title}
        </h2>

        {description && (
          <p
            id="modal-description"
            className="text-sm text-muted-foreground mb-4"
          >
            {description}
          </p>
        )}

        {children}
      </div>
    </div>
  );
}

/**
 * Tooltip accesible
 */
interface AccessibleTooltipProps {
  children: React.ReactNode;
  content: string;
  side?: "top" | "right" | "bottom" | "left";
}

export function AccessibleTooltip({
  children,
  content,
  side = "top",
}: AccessibleTooltipProps) {
  const [isVisible, setIsVisible] = React.useState(false);
  const tooltipId = React.useId();

  return (
    <div className="relative inline-block">
      <div
        aria-describedby={isVisible ? tooltipId : undefined}
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        onFocus={() => setIsVisible(true)}
        onBlur={() => setIsVisible(false)}
      >
        {children}
      </div>

      {isVisible && (
        <div
          id={tooltipId}
          role="tooltip"
          className={cn(
            "absolute z-50 px-2 py-1 text-sm bg-popover text-popover-foreground rounded shadow-md",
            "border border-border",
            side === "top" && "bottom-full left-1/2 -translate-x-1/2 mb-2",
            side === "right" && "left-full top-1/2 -translate-y-1/2 ml-2",
            side === "bottom" && "top-full left-1/2 -translate-x-1/2 mt-2",
            side === "left" && "right-full top-1/2 -translate-y-1/2 mr-2",
          )}
        >
          {content}
        </div>
      )}
    </div>
  );
}

/**
 * Indicador de progreso accesible
 */
interface AccessibleProgressProps {
  value: number;
  max?: number;
  label?: string;
  className?: string;
}

export function AccessibleProgress({
  value,
  max = 100,
  label = "Progreso",
  className,
}: AccessibleProgressProps) {
  const percentage = Math.round((value / max) * 100);

  return (
    <div className={cn("w-full", className)}>
      {label && (
        <div className="flex justify-between text-sm mb-1">
          <span>{label}</span>
          <span>{percentage}%</span>
        </div>
      )}

      <div
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={label}
        className="w-full bg-secondary rounded-full h-2"
      >
        <div
          className="bg-primary h-2 rounded-full transition-all duration-300"
          style={{ width: `${percentage}%` }}
        />
      </div>

      <ScreenReaderOnly>
        {label}: {percentage}% completado
      </ScreenReaderOnly>
    </div>
  );
}
