"use client";

import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { type Certificate, supabase } from "@/lib/supabase";

export default function CertificateManagement() {
  const [certificates, setCertificates] = useState<
    (Certificate & {
      student_name: string;
      course_name: string;
    })[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const fetchCertificates = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch certificates with student and course info using Supabase joins
      const { data, error } = await supabase
        .from("certificates")
        .select(`
          *,
          student:user_id(first_name, last_name),
          course:course_id(title)
        `)
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Transform data to match expected format
      const formattedData = data.map((cert) => {
        // Construir student_name a partir de first_name y last_name
        const firstName = cert.student?.first_name || "";
        const lastName = cert.student?.last_name || "";
        const studentName =
          `${firstName} ${lastName}`.trim() || "Estudiante no encontrado";

        return {
          ...cert,
          student_name: studentName,
          course_name: cert.course?.title || "Curso no encontrado",
          certificate_number:
            cert.certificate_number || "Número de certificado no disponible",
        };
      });

      setCertificates(formattedData);
    } catch (error: unknown) {
      console.error("Error fetching certificates:", error);
      setError("No se pudieron cargar los certificados");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCertificates();
  }, [fetchCertificates]);

  // Filter certificates based on search term
  const filteredCertificates = certificates.filter(
    (cert) =>
      cert.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cert.course_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cert.certificate_number.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between mb-8">
        <div>
          <h3 className="text-2xl font-semibold text-gray-900">
            Gestión de Certificados
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Emite y administra certificados para tus alumnos.
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link
            href="/panel-admin/certificados/nuevo"
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Emitir Nuevo Certificado
          </Link>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden sm:rounded-md mb-6">
        <div className="px-4 py-4 sm:px-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="Buscar por alumno, curso o número de certificado..."
              />
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-48">
            <p className="text-gray-500">Cargando certificados...</p>
          </div>
        ) : error ? (
          <div className="p-4">
            <div className="bg-red-50 border-l-4 border-red-500 p-4">
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        ) : filteredCertificates.length === 0 ? (
          <div className="px-4 py-5 text-center">
            <p className="text-gray-500">
              {searchTerm
                ? "No se encontraron certificados que coincidan con la búsqueda."
                : "Aún no hay certificados emitidos."}
            </p>
            <p className="mt-2 text-sm text-gray-500">
              {searchTerm
                ? "Intenta con otros términos de búsqueda."
                : "Crea tu primer certificado haciendo clic en 'Emitir Nuevo Certificado'."}
            </p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {filteredCertificates.map((certificate) => (
              <li key={certificate.id}>
                <div className="block hover:bg-gray-50">
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-indigo-600 truncate">
                          {certificate.certificate_number}
                        </p>
                        <div className="ml-2 flex-shrink-0 flex">
                          <p
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                            ${
                              certificate.status === "ACTIVE"
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {certificate.status}
                          </p>
                        </div>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <Link
                          href={`/panel-admin/certificados/${certificate.id}`}
                          className="font-medium text-indigo-600 hover:text-indigo-500"
                        >
                          Ver Detalles
                        </Link>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex sm:flex-col">
                        <p className="flex items-center text-sm text-gray-500">
                          Estudiante: {certificate.student_name}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                          Curso: {certificate.course_name}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Emitido:{" "}
                          {new Date(certificate.issue_date).toLocaleDateString(
                            "es-ES",
                          )}
                          {certificate.expiry_date && (
                            <span>
                              {" "}
                              | Vence:{" "}
                              {new Date(
                                certificate.expiry_date,
                              ).toLocaleDateString("es-ES")}
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
