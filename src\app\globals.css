@import "tw-animate-css";
@import "./accessibility.css";
@import "../styles/themes/enhanced-theme.css";
@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@700;900&family=Open+Sans:wght@400;600&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Legacy styles - now handled by theme variables in @layer base */

.button-primary {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  font-family: "Montserrat", sans-serif;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 700;
  transition: background 0.2s;
}
.button-primary:hover {
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* CSS variables are now defined in enhanced-theme.css */

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  html,
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family:
      "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
      Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    min-height: 100vh;
  }
  body {
    display: flex;
    flex-direction: column;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Montserrat", sans-serif;
    color: hsl(var(--foreground));
  }
  a {
    color: hsl(var(--primary));
    text-decoration: underline;
  }
  main {
    flex: 1;
  }
  footer {
    margin-top: auto;
  }
}

/* Animaciones y efectos */
@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out forwards;
}

.hero-gradient {
  background: linear-gradient(135deg, #1da1f2 0%, #7b3fe4 100%);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hero-gradient::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* Eliminada la imagen de fondo */
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.2;
  z-index: 0;
}

.btn-gradient {
  background: linear-gradient(90deg, #1da1f2 0%, #7b3fe4 100%);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(90deg, #0c8bd9 0%, #6232c5 100%);
  box-shadow: 0 4px 12px rgba(29, 161, 242, 0.3);
}

.subtle-pattern {
  background-image: radial-gradient(
    rgba(29, 161, 242, 0.05) 1px,
    transparent 1px
  );
  background-size: 20px 20px;
}

/*
  Inicialmente visible para el renderizado del servidor,
  luego se oculta en el cliente y se muestra con animación al hacer scroll
*/
.section-fade {
  opacity: 1; /* Inicialmente visible para SSR */
  transform: translateY(0);
  transition:
    opacity 0.6s ease,
    transform 0.6s ease;
}

/* En el cliente, después de la hidratación, se oculta y luego se muestra con animación */
html.js .section-fade:not(.visible) {
  opacity: 0.01;
  transform: translateY(20px);
}

.section-fade.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Asegurar que en el servidor y al imprimir se renderice con opacidad 1 */
@media print {
  .section-fade {
    opacity: 1;
    transform: none;
  }
}

.card-hover:hover {
  transform: scale(1.02);
  transition: all 0.3s ease;
}

input:focus,
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(29, 161, 242, 0.3);
}
