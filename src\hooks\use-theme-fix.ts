"use client";

import { useTheme } from "next-themes";
import { useEffect } from "react";

export function useThemeFix() {
  const { theme, systemTheme, resolvedTheme } = useTheme();

  useEffect(() => {
    // Force theme application on mount
    const applyTheme = () => {
      const effectiveTheme = theme === "system" ? systemTheme : theme;

      // Remove all theme classes
      document.documentElement.classList.remove("light", "dark");

      // Apply the correct theme class
      if (effectiveTheme === "dark") {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.add("light");
      }

      console.log("Theme applied:", {
        theme,
        systemTheme,
        resolvedTheme,
        effectiveTheme,
        documentClasses: document.documentElement.className,
      });
    };

    // Apply theme immediately
    applyTheme();

    // Listen for system theme changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleSystemThemeChange = () => {
      if (theme === "system") {
        applyTheme();
      }
    };

    mediaQuery.addEventListener("change", handleSystemThemeChange);

    return () => {
      mediaQuery.removeEventListener("change", handleSystemThemeChange);
    };
  }, [theme, systemTheme, resolvedTheme]);

  return { theme, systemTheme, resolvedTheme };
}
