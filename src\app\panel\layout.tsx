"use client";

import {
  Award,
  BarChart3,
  Book<PERSON>pen,
  Home,
  LogOut,
  Menu,
  UserCheck,
  Users,
  X,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { AnimateIn } from "@/components/ui/animations";
import { supabase } from "@/lib/supabase";

// Definir los elementos de navegación por rol
const getNavigationItems = (userRole: string) => {
  const baseItems = [
    {
      name: "Dashboard",
      href: "/panel",
      icon: Home,
      roles: ["admin", "student", "instructor"],
    },
  ];

  const adminItems = [
    {
      name: "Alumnos",
      href: "/panel/alumnos",
      icon: Users,
      roles: ["admin"],
    },
    {
      name: "Certificado<PERSON>",
      href: "/panel/certificados",
      icon: Award,
      roles: ["admin", "student"],
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      href: "/panel/cursos",
      icon: BookOpen,
      roles: ["admin", "instructor"],
    },
    {
      name: "Instructores",
      href: "/panel/instructores",
      icon: UserCheck,
      roles: ["admin"],
    },
    {
      name: "Reportes",
      href: "/panel/reportes",
      icon: BarChart3,
      roles: ["admin"],
    },
  ];

  const studentItems = [
    {
      name: "Mis Certificados",
      href: "/panel/mis-certificados",
      icon: Award,
      roles: ["student"],
    },
  ];

  const allItems = [...baseItems, ...adminItems, ...studentItems];

  return allItems.filter((item) => item.roles.includes(userRole));
};

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

export default function UnifiedPanelLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Obtener información del usuario
  const fetchUserData = useCallback(async () => {
    try {
      const {
        data: { user: authUser },
        error: authError,
      } = await supabase.auth.getUser();

      if (authError || !authUser) {
        router.push("/login");
        return;
      }

      const { data: profile, error: profileError } = await supabase
        .from("users")
        .select("id, email, first_name, last_name, role")
        .eq("id", authUser.id)
        .single();

      if (profileError) {
        console.error("Error fetching user profile:", profileError);
        router.push("/login");
        return;
      }

      setUser(profile);
    } catch (error) {
      console.error("Error in fetchUserData:", error);
      router.push("/login");
    } finally {
      setLoading(false);
    }
  }, [router]);

  useEffect(() => {
    setMounted(true);
    fetchUserData();
  }, [fetchUserData]);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    await supabase.auth.signOut();
    router.push("/login");
  };

  // Si no está montado o cargando, mostrar loading
  if (!mounted || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Si no hay usuario, no renderizar nada (se redirigirá)
  if (!user) {
    return null;
  }

  const navigationItems = getNavigationItems(user.role);

  // Actualizar el estado current de los elementos de navegación
  const updatedNavigationItems = navigationItems.map((item) => ({
    ...item,
    current:
      pathname === item.href ||
      (item.href !== "/panel" && pathname.startsWith(item.href)),
  }));

  const getUserDisplayName = () => {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    }
    return user.email;
  };

  const getRoleDisplayName = (role: string) => {
    const roleNames = {
      admin: "Administrador",
      student: "Alumno",
      instructor: "Instructor",
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  return (
    <div className="min-h-screen bg-background flex">
      {/* Sidebar */}
      <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
        <div className="flex flex-col flex-grow bg-card border-r border-border pt-5 pb-4 overflow-y-auto">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-4">
            <Link href="/panel" className="flex items-center">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                <BarChart3 className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold text-foreground">
                QR CURSE
              </span>
            </Link>
          </div>

          {/* User Info */}
          <div className="mt-4 px-4">
            <div className="bg-muted rounded-lg p-3">
              <p className="text-sm font-medium text-foreground truncate">
                {getUserDisplayName()}
              </p>
              <p className="text-xs text-muted-foreground">
                {getRoleDisplayName(user.role)}
              </p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="mt-6 flex-1 px-2 space-y-1">
            {updatedNavigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`${
                    item.current
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  } group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors`}
                >
                  <Icon
                    className={`${
                      item.current
                        ? "text-primary-foreground"
                        : "text-muted-foreground group-hover:text-foreground"
                    } mr-3 flex-shrink-0 h-5 w-5 transition-colors`}
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* Bottom section */}
          <div className="flex-shrink-0 px-2 space-y-1">
            <div className="border-t border-border pt-4">
              <div className="flex items-center justify-between px-3 py-2">
                <ThemeToggle variant="ghost" size="sm" />
                <button
                  type="button"
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                  className="p-2 text-muted-foreground hover:text-foreground hover:bg-muted rounded-lg transition-colors"
                  title="Cerrar sesión"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 flex z-40 lg:hidden">
          <button
            type="button"
            className="fixed inset-0 bg-background/80 backdrop-blur-sm"
            onClick={() => setSidebarOpen(false)}
            onKeyDown={(e) => {
              if (e.key === "Escape") {
                setSidebarOpen(false);
              }
            }}
            aria-label="Cerrar menú"
          />
          <div className="relative flex-1 flex flex-col max-w-xs w-full bg-card">
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                type="button"
                className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                onClick={() => setSidebarOpen(false)}
              >
                <X className="h-6 w-6 text-foreground" />
              </button>
            </div>
            {/* Mobile navigation content - same as desktop */}
            <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
              <div className="flex-shrink-0 flex items-center px-4">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                  <BarChart3 className="w-5 h-5 text-primary-foreground" />
                </div>
                <span className="text-xl font-bold text-foreground">
                  QR CURSE
                </span>
              </div>

              {/* User Info Mobile */}
              <div className="mt-4 px-4">
                <div className="bg-muted rounded-lg p-3">
                  <p className="text-sm font-medium text-foreground truncate">
                    {getUserDisplayName()}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {getRoleDisplayName(user.role)}
                  </p>
                </div>
              </div>

              <nav className="mt-6 px-2 space-y-1">
                {updatedNavigationItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`${
                        item.current
                          ? "bg-primary text-primary-foreground"
                          : "text-muted-foreground hover:bg-muted hover:text-foreground"
                      } group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors`}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <Icon className="mr-3 flex-shrink-0 h-5 w-5" />
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top bar for mobile */}
        <div className="sticky top-0 z-10 lg:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-background border-b border-border">
          <button
            type="button"
            className="h-12 w-12 inline-flex items-center justify-center rounded-md text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <AnimateIn animation="fade" duration="normal">
                {children}
              </AnimateIn>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
