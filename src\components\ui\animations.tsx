/**
 * @fileoverview Modern Animation System (2025 UI/UX Trends)
 *
 * Sistema de animaciones moderno que implementa:
 * - Micro-interacciones fluidas
 * - Transiciones contextuales
 * - Efectos de entrada y salida
 * - Animaciones de carga
 * - Efectos de hover avanzados
 */

"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

// ============================================================================
// Animation Hooks
// ============================================================================

/**
 * Hook para animaciones de entrada escalonada
 */
export function useStaggeredAnimation(itemCount: number, delay: number = 100) {
  const [visibleItems, setVisibleItems] = React.useState(0);

  React.useEffect(() => {
    const timer = setInterval(() => {
      setVisibleItems((prev) => {
        if (prev >= itemCount) {
          clearInterval(timer);
          return prev;
        }
        return prev + 1;
      });
    }, delay);

    return () => clearInterval(timer);
  }, [itemCount, delay]);

  return visibleItems;
}

/**
 * Hook para animaciones de scroll
 */
export function useScrollAnimation(threshold: number = 0.1) {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold },
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold]);

  return { ref, isVisible };
}

// ============================================================================
// Animation Components
// ============================================================================

/**
 * Contenedor de animación de entrada
 */
interface AnimateInProps {
  children: React.ReactNode;
  animation?:
    | "fade"
    | "slide-up"
    | "slide-down"
    | "slide-left"
    | "slide-right"
    | "scale"
    | "bounce";
  duration?: "fast" | "normal" | "slow";
  delay?: number;
  className?: string;
}

const animationClasses = {
  fade: "animate-fade-in-up",
  "slide-up": "animate-slide-in-up",
  "slide-down": "animate-slide-in-down",
  "slide-left": "animate-slide-in-left",
  "slide-right": "animate-slide-in-right",
  scale: "animate-scale-in",
  bounce: "animate-bounce-in",
};

const durationClasses = {
  fast: "duration-150",
  normal: "duration-300",
  slow: "duration-500",
};

export function AnimateIn({
  children,
  animation = "fade",
  duration = "normal",
  delay = 0,
  className,
}: AnimateInProps) {
  return (
    <div
      className={cn(
        animationClasses[animation],
        durationClasses[duration],
        className,
      )}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
}

/**
 * Contenedor de animación de scroll
 */
interface ScrollAnimateProps {
  children: React.ReactNode;
  animation?: "fade" | "slide-up" | "scale";
  threshold?: number;
  className?: string;
}

export function ScrollAnimate({
  children,
  animation = "fade",
  threshold = 0.1,
  className,
}: ScrollAnimateProps) {
  const { ref, isVisible } = useScrollAnimation(threshold);

  return (
    <div
      ref={ref}
      className={cn(
        "transition-all duration-700 ease-out",
        isVisible
          ? "opacity-100 translate-y-0 scale-100"
          : "opacity-0 translate-y-8 scale-95",
        className,
      )}
    >
      {children}
    </div>
  );
}

/**
 * Lista con animación escalonada
 */
interface StaggeredListProps {
  children: React.ReactNode[];
  delay?: number;
  className?: string;
}

export function StaggeredList({
  children,
  delay = 100,
  className,
}: StaggeredListProps) {
  const visibleItems = useStaggeredAnimation(children.length, delay);

  return (
    <div className={className}>
      {children.map((child, index) => (
        <div
          key={index}
          className={cn(
            "transition-all duration-300 ease-out",
            index < visibleItems
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-4",
          )}
        >
          {child}
        </div>
      ))}
    </div>
  );
}

/**
 * Efecto de hover con transformación
 */
interface HoverEffectProps {
  children: React.ReactNode;
  effect?: "lift" | "scale" | "glow" | "tilt" | "bounce";
  intensity?: "subtle" | "normal" | "strong";
  className?: string;
}

const hoverEffects = {
  lift: {
    subtle: "hover:-translate-y-1 hover:shadow-md",
    normal: "hover:-translate-y-2 hover:shadow-lg",
    strong: "hover:-translate-y-3 hover:shadow-xl",
  },
  scale: {
    subtle: "hover:scale-[1.02]",
    normal: "hover:scale-105",
    strong: "hover:scale-110",
  },
  glow: {
    subtle: "hover:shadow-[0_0_20px_rgba(59,130,246,0.2)]",
    normal: "hover:shadow-[0_0_30px_rgba(59,130,246,0.3)]",
    strong: "hover:shadow-[0_0_40px_rgba(59,130,246,0.4)]",
  },
  tilt: {
    subtle: "hover:rotate-1",
    normal: "hover:rotate-2",
    strong: "hover:rotate-3",
  },
  bounce: {
    subtle: "hover:animate-bounce-subtle",
    normal: "hover:animate-bounce",
    strong: "hover:animate-bounce-strong",
  },
};

export function HoverEffect({
  children,
  effect = "lift",
  intensity = "normal",
  className,
}: HoverEffectProps) {
  return (
    <div
      className={cn(
        "transition-all duration-200 ease-out cursor-pointer",
        hoverEffects[effect][intensity],
        className,
      )}
    >
      {children}
    </div>
  );
}

/**
 * Indicador de carga con animación
 */
interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "default" | "dots" | "pulse" | "bars";
  className?: string;
}

export function LoadingSpinner({
  size = "md",
  variant = "default",
  className,
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-12 h-12",
  };

  if (variant === "dots") {
    return (
      <div className={cn("flex space-x-1", className)}>
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              "bg-primary rounded-full animate-pulse",
              size === "sm"
                ? "w-1 h-1"
                : size === "md"
                  ? "w-2 h-2"
                  : size === "lg"
                    ? "w-3 h-3"
                    : "w-4 h-4",
            )}
            style={{ animationDelay: `${i * 0.2}s` }}
          />
        ))}
      </div>
    );
  }

  if (variant === "pulse") {
    return (
      <div
        className={cn(
          "bg-primary rounded-full animate-pulse",
          sizeClasses[size],
          className,
        )}
      />
    );
  }

  if (variant === "bars") {
    return (
      <div className={cn("flex space-x-1", className)}>
        {[0, 1, 2, 3].map((i) => (
          <div
            key={i}
            className={cn(
              "bg-primary animate-pulse",
              size === "sm"
                ? "w-1 h-4"
                : size === "md"
                  ? "w-1 h-6"
                  : size === "lg"
                    ? "w-2 h-8"
                    : "w-2 h-12",
            )}
            style={{ animationDelay: `${i * 0.15}s` }}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn(
        "border-2 border-primary border-t-transparent rounded-full animate-spin",
        sizeClasses[size],
        className,
      )}
    />
  );
}

/**
 * Transición de página
 */
interface PageTransitionProps {
  children: React.ReactNode;
  isLoading?: boolean;
}

export function PageTransition({
  children,
  isLoading = false,
}: PageTransitionProps) {
  return (
    <div className="relative">
      <div
        className={cn(
          "transition-all duration-300 ease-in-out",
          isLoading ? "opacity-50 scale-95" : "opacity-100 scale-100",
        )}
      >
        {children}
      </div>

      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/50 backdrop-blur-sm">
          <LoadingSpinner size="lg" />
        </div>
      )}
    </div>
  );
}

/**
 * Efecto de escritura (typewriter)
 */
interface TypewriterProps {
  text: string;
  speed?: number;
  className?: string;
}

export function Typewriter({ text, speed = 50, className }: TypewriterProps) {
  const [displayText, setDisplayText] = React.useState("");
  const [currentIndex, setCurrentIndex] = React.useState(0);

  React.useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayText((prev) => prev + text[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, speed);

      return () => clearTimeout(timer);
    }
  }, [currentIndex, text, speed]);

  return (
    <span className={className}>
      {displayText}
      <span className="animate-pulse">|</span>
    </span>
  );
}
