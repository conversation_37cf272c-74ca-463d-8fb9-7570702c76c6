"use client";

import { Alert<PERSON>riangle, Home, RefreshCw } from "lucide-react";
import React from "react";
import { ModernButton } from "./modern-button";
import {
  ModernCard,
  ModernCardContent,
  ModernCardHeader,
  ModernCardTitle,
} from "./modern-card";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Error caught by boundary:", error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return (
          <FallbackComponent error={this.state.error!} retry={this.retry} />
        );
      }

      return (
        <DefaultErrorFallback error={this.state.error!} retry={this.retry} />
      );
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error: Error;
  retry: () => void;
}

export function DefaultErrorFallback({ error, retry }: ErrorFallbackProps) {
  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <ModernCard variant="glass" className="max-w-md w-full">
        <ModernCardHeader className="text-center">
          <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-destructive" />
          </div>
          <ModernCardTitle className="text-destructive">
            Algo salió mal
          </ModernCardTitle>
        </ModernCardHeader>
        <ModernCardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            Ha ocurrido un error inesperado. Por favor, intenta nuevamente.
          </p>

          {process.env.NODE_ENV === "development" && (
            <details className="text-left">
              <summary className="cursor-pointer text-sm font-medium mb-2">
                Detalles del error (desarrollo)
              </summary>
              <pre className="text-xs bg-muted p-3 rounded-lg overflow-auto">
                {error.message}
                {error.stack && `\n\n${error.stack}`}
              </pre>
            </details>
          )}

          <div className="flex gap-2 justify-center">
            <ModernButton
              variant="outline"
              onClick={retry}
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Reintentar
            </ModernButton>
            <ModernButton
              variant="default"
              onClick={() => (window.location.href = "/panel-admin")}
              className="flex items-center gap-2"
            >
              <Home className="w-4 h-4" />
              Ir al inicio
            </ModernButton>
          </div>
        </ModernCardContent>
      </ModernCard>
    </div>
  );
}

// Hook para manejo de errores en componentes funcionales
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const handleError = React.useCallback((error: Error) => {
    console.error("Error handled:", error);
    setError(error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  React.useEffect(() => {
    if (error) {
      // Opcional: reportar error a servicio de monitoreo
      console.error("Error state updated:", error);
    }
  }, [error]);

  return {
    error,
    handleError,
    clearError,
    hasError: !!error,
  };
}

// Componente de error para páginas específicas
export function PageError({
  title = "Error en la página",
  message = "Ha ocurrido un error al cargar esta página.",
  onRetry,
  showRetry = true,
}: {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetry?: boolean;
}) {
  return (
    <div className="flex items-center justify-center min-h-[300px] p-4">
      <ModernCard variant="minimal" className="max-w-md w-full text-center">
        <ModernCardContent className="p-6 space-y-4">
          <div className="w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mx-auto">
            <AlertTriangle className="w-6 h-6 text-destructive" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-2">
              {title}
            </h3>
            <p className="text-muted-foreground">{message}</p>
          </div>
          {showRetry && onRetry && (
            <ModernButton
              variant="outline"
              onClick={onRetry}
              className="flex items-center gap-2 mx-auto"
            >
              <RefreshCw className="w-4 h-4" />
              Reintentar
            </ModernButton>
          )}
        </ModernCardContent>
      </ModernCard>
    </div>
  );
}
