"use client";

import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";

export default function TestSupabase() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [students, setStudents] = useState<unknown[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<string>(
    "Checking connection...",
  );

  useEffect(() => {
    async function testConnection() {
      try {
        // Test basic connection
        const { error: connectionError } = await supabase
          .from("_dummy_query_for_connection_test_")
          .select("*", { head: true })
          .limit(0);

        if (
          connectionError &&
          connectionError.code !== "42P01" &&
          !connectionError.message.includes("does not exist")
        ) {
          setConnectionStatus(`Connection error: ${connectionError.message}`);
        } else {
          setConnectionStatus("Connection successful");
        }

        // Fetch students
        const { data: studentsData, error: studentsError } = await supabase
          .from("users")
          .select("id, email, first_name, last_name, identity_document")
          .eq("role", "student");

        if (studentsError) {
          console.error("Error fetching students:", studentsError);
          setError(`Error fetching students: ${studentsError.message}`);
          return;
        }

        console.log("Fetched students:", studentsData);
        setStudents(studentsData || []);
      } catch (err: unknown) {
        console.error("Error in test:", err);
        setError(err.message || "Unknown error");
      } finally {
        setLoading(false);
      }
    }

    testConnection();
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>

      <div className="mb-4 p-4 border rounded">
        <h2 className="text-lg font-semibold mb-2">Connection Status</h2>
        <p
          className={
            connectionStatus.includes("error")
              ? "text-red-500"
              : "text-green-500"
          }
        >
          {connectionStatus}
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded">
          <h2 className="text-lg font-semibold mb-2 text-red-700">Error</h2>
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="mb-4">
        <h2 className="text-lg font-semibold mb-2">Students Data</h2>
        {loading ? (
          <p>Loading students...</p>
        ) : students.length === 0 ? (
          <p>No students found</p>
        ) : (
          <div className="border rounded overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    RUT
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {students.map((student) => (
                  <tr key={student.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {student.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {`${student.first_name || ""} ${student.last_name || ""}`.trim() ||
                        "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {student.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {student.identity_document || "N/A"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-2">Debug Information</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96">
          {JSON.stringify({ students }, null, 2)}
        </pre>
      </div>
    </div>
  );
}
