"use client";

import { useEffect, useState } from "react";
// Definir las funciones localmente para evitar problemas de importación
import { supabase } from "@/lib/supabase";

// Función para verificar la sesión del usuario
async function checkUserSession() {
  try {
    // Intentar obtener la sesión actual desde Supabase
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error("[Auth] Error al verificar sesión:", error);
      return null;
    }

    // Si hay sesión, devolver los datos del usuario
    if (data?.session) {
      console.log("[Auth] Sesión activa:", data.session.user.id);
      return data.session;
    }

    console.log("[Auth] No hay sesión activa");
    return null;
  } catch (err) {
    console.error("[Auth] Error inesperado al verificar sesión:", err);
    return null;
  }
}

// Función para registrar cookies
function logBrowserCookies() {
  if (typeof document === "undefined") return;

  console.log("[Auth] Cookies disponibles en navegador:", document.cookie);

  // Buscar específicamente cookies relacionadas con Supabase
  const supabaseCookies = document.cookie
    .split("; ")
    .filter((cookie) => cookie.includes("sb-") || cookie.includes("supabase"))
    .map((cookie) => cookie.trim());

  if (supabaseCookies.length > 0) {
    console.log("[Auth] Cookies de Supabase encontradas:", supabaseCookies);
  } else {
    console.log("[Auth] No se encontraron cookies de Supabase");
  }
}

// Función para forzar recarga
function forceReload(path: string) {
  if (typeof window === "undefined") return;

  console.log(`[Auth] Forzando recarga completa a: ${path}`);
  window.location.href = path;
}

import type { Session } from "@supabase/supabase-js";

export default function SessionDebugger() {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [cookieInfo, setCookieInfo] = useState<string[]>([]);

  useEffect(() => {
    async function checkSession() {
      setLoading(true);

      // Loguear cookies del navegador
      logBrowserCookies();

      // Capturar información de cookies para mostrar
      if (typeof document !== "undefined") {
        const supabaseCookies = document.cookie
          .split("; ")
          .filter(
            (cookie) => cookie.includes("sb-") || cookie.includes("supabase"),
          )
          .map((cookie) => cookie.trim());
        setCookieInfo(supabaseCookies);
      }

      // Verificar estado de sesión
      const sessionData = await checkUserSession();
      setSession(sessionData);
      setLoading(false);
    }

    checkSession();

    // Verificar cada 3 segundos mientras se está depurando
    const interval = setInterval(checkSession, 3000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded shadow-lg z-50 max-w-md opacity-90">
        <div className="flex items-center space-x-2">
          <svg
            className="animate-spin h-5 w-5 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            aria-label="Cargando"
          >
            <title>Cargando sesión</title>
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-sm font-medium">Verificando estado de sesión...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded shadow-lg z-50 max-w-md opacity-90 backdrop-blur">
      <div className="flex justify-between items-start mb-2">
        <h3 className="text-lg font-bold">Estado de Sesión</h3>
        <div className="text-xs bg-gray-700 px-2 py-1 rounded">Debug</div>
      </div>

      {session ? (
        <div>
          <div className="flex items-center space-x-2 mb-1">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <p className="text-sm text-green-400 font-medium">Sesión activa</p>
          </div>
          <div className="border-l-2 border-gray-700 pl-3 ml-1 mt-2 mb-2 space-y-1">
            <p className="text-xs text-gray-300">
              <span className="text-gray-500">Usuario:</span>{" "}
              {session.user.email}
            </p>
            <p className="text-xs text-gray-300">
              <span className="text-gray-500">ID:</span>{" "}
              {session.user.id.substring(0, 8)}...
            </p>
            <p className="text-xs text-gray-300">
              <span className="text-gray-500">Expira:</span>{" "}
              {session.expires_at
                ? new Date(session.expires_at * 1000).toLocaleTimeString()
                : "No disponible"}
            </p>
          </div>

          {cookieInfo.length > 0 && (
            <div className="mt-2 mb-3">
              <p className="text-xs text-gray-400 mb-1">
                Cookies de autenticación:
              </p>
              <div className="text-xs text-gray-500 max-h-16 overflow-y-auto bg-gray-900 p-1 rounded text-xs">
                {cookieInfo.map((cookie) => (
                  <div key={cookie.substring(0, 50)} className="truncate">
                    {cookie.substring(0, 30)}...
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex space-x-2 mt-2">
            <button
              type="button"
              onClick={() => forceReload("/panel-alumno")}
              className="text-xs bg-blue-600 hover:bg-blue-700 py-1 px-2 rounded flex-1"
            >
              Panel Alumno
            </button>
            <button
              type="button"
              onClick={() => forceReload("/panel-admin")}
              className="text-xs bg-purple-600 hover:bg-purple-700 py-1 px-2 rounded flex-1"
            >
              Panel Admin
            </button>
          </div>
        </div>
      ) : (
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-red-400 rounded-full"></div>
            <p className="text-sm text-red-400 font-medium">
              No hay sesión activa
            </p>
          </div>
          <p className="text-xs text-gray-400 mt-1 mb-3">
            Ingresa tus credenciales para iniciar sesión.
          </p>

          {cookieInfo.length > 0 && (
            <div className="mt-2 mb-2">
              <p className="text-xs text-gray-400 mb-1">Cookies residuales:</p>
              <div className="text-xs text-gray-500 max-h-16 overflow-y-auto bg-gray-900 p-1 rounded">
                {cookieInfo.map((cookie) => (
                  <div key={cookie.substring(0, 50)} className="truncate">
                    {cookie.substring(0, 30)}...
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
