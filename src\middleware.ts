import type { CookieOptions } from "@supabase/ssr";
import { createServerClient } from "@supabase/ssr";

import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  // Definir claramente las rutas
  const PUBLIC_ROUTES = [
    "/login",
    "/register",
    "/",
    "/auth/callback",
    "/verificar-certificado",
    "/certificado-preview",
  ];
  const PANEL_ROUTES = ["/panel"];
  const PROTECTED_ROUTES = [...PANEL_ROUTES];

  const path = request.nextUrl.pathname;
  console.log(`[Middleware] Procesando ruta: ${path}`);

  // Crear respuesta para modificar después
  const response = NextResponse.next({
    request: { headers: request.headers },
  });

  try {
    // PASO 1: Para rutas públicas, no hacer verificación de autenticación
    // Esto es importante para evitar ciclos de redirección
    if (
      PUBLIC_ROUTES.some(
        (route) => path === route || path.startsWith(`${route}/`),
      )
    ) {
      console.log(
        `[Middleware] Ruta pública detectada: ${path}, acceso permitido sin verificación`,
      );
      return response;
    }

    // PASO 2: Para rutas protegidas, crear cliente Supabase y verificar sesión
    console.log(`[Middleware] Verificando acceso a ruta protegida: ${path}`);

    // Crear cliente Supabase para verificar la sesión
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL as string,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
      {
        cookies: {
          get(name: string) {
            const cookie = request.cookies.get(name)?.value;
            console.log(
              `[Middleware] Leyendo cookie ${name}: ${cookie ? "presente" : "ausente"}`,
            );
            return cookie;
          },
          set(name: string, value: string, options: CookieOptions) {
            console.log(`[Middleware] Estableciendo cookie ${name}`);
            response.cookies.set({
              name,
              value,
              ...options,
            });
          },
          remove(name: string, options: CookieOptions) {
            console.log(`[Middleware] Eliminando cookie ${name}`);
            response.cookies.delete({
              name,
              ...options,
            });
          },
        },
      },
    );

    // Get user information
    const {
      data: { user },
    } = await supabase.auth.getUser();

    // Si es una ruta protegida y no hay sesión, redirigir a login
    if (!user) {
      console.log(`[Middleware] Usuario no autenticado, redirigiendo a login`);
      const callbackUrl = encodeURIComponent(path);
      return NextResponse.redirect(
        new URL(`/login?redirect=${callbackUrl}`, request.url),
      );
    }

    console.log(`[Middleware] Sesión detectada: ${user.id}`);

    // PASO 3: Para rutas del panel, verificar que el usuario tenga un rol válido
    if (
      PANEL_ROUTES.some(
        (route) => path === route || path.startsWith(`${route}/`),
      )
    ) {
      console.log(
        `[Middleware] Verificando acceso al panel para: ${path}`,
      );
      try {
        const { data: profile, error } = await supabase
          .from("users")
          .select("role")
          .eq("id", user.id)
          .single();

        if (error) {
          console.error(
            `[Middleware] Error al verificar perfil de usuario: ${error.message}`,
          );
          return NextResponse.redirect(new URL("/login", request.url));
        }

        console.log(`[Middleware] Rol del usuario: ${profile?.role}`);
        if (!profile || !["admin", "student", "instructor"].includes(profile.role.toLowerCase())) {
          console.log(
            "[Middleware] Usuario sin rol válido, redirigiendo a login",
          );
          return NextResponse.redirect(new URL("/login", request.url));
        }

        console.log(
          `[Middleware] Acceso al panel concedido para: ${path}`,
        );
      } catch (error) {
        console.error(
          "[Middleware] Error en middleware (verificación de acceso):",
          error,
        );
        return NextResponse.redirect(new URL("/login", request.url));
      }
    }

    // Si pasa todas las verificaciones, permitir el acceso
    console.log(`[Middleware] Permitiendo acceso a ruta protegida: ${path}`);
    return response;
  } catch (error) {
    console.error("[Middleware] Error general en middleware:", error);

    // Si hay un error para una ruta protegida, redirigir a login por seguridad
    if (
      PROTECTED_ROUTES.some(
        (route) => path === route || path.startsWith(`${route}/`),
      )
    ) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    return response;
  }
}

// Especificar las rutas donde debe ejecutarse el middleware
export const config = {
  matcher: [
    "/panel/:path*",
    "/login",
    "/register",
    "/auth/callback",
    "/verificar-certificado",
    "/certificado-preview/:path*",
    "/",
  ],
};
