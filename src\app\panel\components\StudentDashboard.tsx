"use client";

import { Award, Calendar, Download, Eye, FileText } from "lucide-react";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { AnimateIn, StaggeredList } from "@/components/ui/animations";
import { PageError } from "@/components/ui/error-boundary";
import { LoadingPage } from "@/components/ui/loading";
import { ModernButton } from "@/components/ui/modern-button";
import {
  ModernCard,
  ModernCardContent,
  StatCard,
} from "@/components/ui/modern-card";
import { ensureValidSession, supabase } from "@/lib/supabase";

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface Course {
  id: string;
  title: string | null;
}

interface Certificate {
  id: string;
  issue_date: string;
  status: string;
  course_id: string | null;
  certificate_number?: string | null;
  course?: Course | null;
}

interface StudentDashboardProps {
  user: User;
}

export default function StudentDashboard({ user }: StudentDashboardProps) {
  const [certificatesCount, setCertificatesCount] = useState(0);
  const [activeCertificatesCount, setActiveCertificatesCount] = useState(0);
  const [recentCertificates, setRecentCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStudentData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Verificar si la sesión está activa y válida
      const isSessionValid = await ensureValidSession();
      if (!isSessionValid) {
        throw new Error("No hay sesión activa o ha expirado");
      }

      // Obtener certificados del usuario
      const { data: certificates, error: certificatesError } = await supabase
        .from("certificates")
        .select(`
          id,
          certificate_number,
          issue_date,
          status,
          course_id,
          courses (
            id,
            title
          )
        `)
        .eq("user_id", user.id)
        .order("issue_date", { ascending: false });

      if (certificatesError) {
        throw certificatesError;
      }

      const certificatesList = certificates || [];
      
      // Calcular estadísticas
      setCertificatesCount(certificatesList.length);
      setActiveCertificatesCount(
        certificatesList.filter(cert => cert.status?.toLowerCase() === "active").length
      );
      
      // Obtener los 3 certificados más recientes
      setRecentCertificates(certificatesList.slice(0, 3));

    } catch (error) {
      console.error("Error fetching student data:", error);
      setError(error instanceof Error ? error.message : "Error desconocido");
    } finally {
      setLoading(false);
    }
  }, [user.id]);

  useEffect(() => {
    fetchStudentData();
  }, [fetchStudentData]);

  if (loading) {
    return <LoadingPage />;
  }

  if (error) {
    return <PageError message={error} onRetry={fetchStudentData} />;
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "expired":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "revoked":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "Activo";
      case "expired":
        return "Expirado";
      case "revoked":
        return "Revocado";
      default:
        return status || "Desconocido";
    }
  };

  return (
    <div className="space-y-8">
      {/* Estadísticas del estudiante */}
      <AnimateIn animation="fade" duration="normal">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <StatCard
            title="Total Certificados"
            value={certificatesCount}
            change="Certificados obtenidos"
            trend="neutral"
            icon={Award}
            color="blue"
          />
          <StatCard
            title="Certificados Activos"
            value={activeCertificatesCount}
            change="Válidos actualmente"
            trend="up"
            icon={FileText}
            color="green"
          />
          <StatCard
            title="Último Certificado"
            value={recentCertificates.length > 0 ? "Reciente" : "Ninguno"}
            change={
              recentCertificates.length > 0
                ? new Date(recentCertificates[0].issue_date).toLocaleDateString()
                : "No disponible"
            }
            trend="neutral"
            icon={Calendar}
            color="purple"
          />
        </div>
      </AnimateIn>

      {/* Acciones rápidas */}
      <AnimateIn animation="slide" direction="up" delay={200}>
        <ModernCard>
          <ModernCardContent>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-foreground">
                Acciones Rápidas
              </h2>
              <Award className="w-5 h-5 text-primary" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Link href="/panel/mis-certificados" className="group block">
                <div className="bg-muted hover:bg-muted/80 rounded-lg p-4 transition-all duration-200 hover:shadow-md border border-border hover:border-primary/20">
                  <div className="flex items-center space-x-3">
                    <div className="bg-blue-500 p-2 rounded-lg">
                      <Eye className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                        Ver Mis Certificados
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        Revisar todos mis certificados
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
              
              <Link href="/cursos" className="group block">
                <div className="bg-muted hover:bg-muted/80 rounded-lg p-4 transition-all duration-200 hover:shadow-md border border-border hover:border-primary/20">
                  <div className="flex items-center space-x-3">
                    <div className="bg-green-500 p-2 rounded-lg">
                      <FileText className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                        Explorar Cursos
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        Ver cursos disponibles
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          </ModernCardContent>
        </ModernCard>
      </AnimateIn>

      {/* Certificados recientes */}
      <AnimateIn animation="slide" direction="up" delay={400}>
        <ModernCard>
          <ModernCardContent>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-foreground">
                Mis Certificados Recientes
              </h2>
              <Link href="/panel/mis-certificados">
                <ModernButton variant="outline" size="sm">
                  Ver Todos
                </ModernButton>
              </Link>
            </div>
            
            {recentCertificates.length > 0 ? (
              <StaggeredList className="space-y-4">
                {recentCertificates.map((certificate) => (
                  <div
                    key={certificate.id}
                    className="bg-muted/50 rounded-lg p-4 border border-border hover:border-primary/20 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3">
                          <Award className="w-5 h-5 text-primary flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <h3 className="text-sm font-medium text-foreground truncate">
                              {certificate.course?.title || "Curso no especificado"}
                            </h3>
                            <div className="flex items-center space-x-2 mt-1">
                              <span className="text-xs text-muted-foreground">
                                #{certificate.certificate_number || "Sin número"}
                              </span>
                              <span className="text-xs text-muted-foreground">•</span>
                              <span className="text-xs text-muted-foreground">
                                {new Date(certificate.issue_date).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                            certificate.status
                          )}`}
                        >
                          {getStatusLabel(certificate.status)}
                        </span>
                        <Link href={`/panel/mis-certificados/${certificate.id}`}>
                          <ModernButton variant="ghost" size="sm">
                            <Eye className="w-4 h-4" />
                          </ModernButton>
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </StaggeredList>
            ) : (
              <div className="text-center py-12">
                <Award className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  No tienes certificados aún
                </h3>
                <p className="text-muted-foreground mb-4">
                  Completa un curso para obtener tu primer certificado
                </p>
                <Link href="/cursos">
                  <ModernButton>
                    Explorar Cursos
                  </ModernButton>
                </Link>
              </div>
            )}
          </ModernCardContent>
        </ModernCard>
      </AnimateIn>
    </div>
  );
}
