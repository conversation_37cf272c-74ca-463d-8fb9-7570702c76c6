import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

/**
 * Verifica si un bucket existe en Supabase Storage y lo crea si no existe
 * @param bucketName Nombre del bucket a verificar/crear
 * @param isPublic Si el bucket debe ser público (true) o privado (false)
 * @returns Un objeto con el resultado de la operación
 */
export async function ensureBucketExists(
  bucketName: string,
  isPublic: boolean = false,
) {
  const supabase = createClientComponentClient();

  try {
    // Verificar si el bucket existe
    const { data: buckets, error: bucketsError } =
      await supabase.storage.listBuckets();

    if (bucketsError) {
      // Si el error es de permisos, asumimos que el bucket ya existe
      // para permitir que la aplicación continúe funcionando
      if (
        bucketsError.message &&
        (bucketsError.message.includes("permission") ||
          bucketsError.message.includes("security policy") ||
          bucketsError.message.includes("not authorized"))
      ) {
        console.warn(
          `No se pueden listar buckets debido a permisos. Asumiendo que '${bucketName}' existe.`,
        );
        return {
          success: true,
          created: false,
          message: `Bucket '${bucketName}' asumido como existente (permisos limitados)`,
        };
      }

      console.error("Error al listar buckets:", bucketsError);
      return {
        success: false,
        error: bucketsError,
        message: `Error al verificar buckets: ${bucketsError.message}`,
      };
    }

    // Verificar si el bucket específico existe
    const bucketExists = buckets?.some((bucket) => bucket.name === bucketName);

    if (bucketExists) {
      console.log(`Bucket '${bucketName}' ya existe`);
      return {
        success: true,
        created: false,
        message: `Bucket '${bucketName}' ya existe`,
      };
    }

    // Si no existe, intentar crearlo
    const { error: createError } = await supabase.storage.createBucket(
      bucketName,
      {
        public: isPublic,
        fileSizeLimit: 5 * 1024 * 1024, // 5MB
      },
    );

    if (createError) {
      // Si el error es de permisos, asumimos que el bucket ya existe
      // para permitir que la aplicación continúe funcionando
      if (
        createError.message &&
        (createError.message.includes("permission") ||
          createError.message.includes("security policy") ||
          createError.message.includes("not authorized"))
      ) {
        console.warn(
          `No se puede crear bucket debido a permisos. Asumiendo que '${bucketName}' existe.`,
        );
        return {
          success: true,
          created: false,
          message: `Bucket '${bucketName}' asumido como existente (permisos limitados)`,
        };
      }

      console.error(`Error al crear bucket '${bucketName}':`, createError);
      return {
        success: false,
        error: createError,
        message: `Error al crear bucket '${bucketName}': ${createError.message}`,
      };
    }

    console.log(`Bucket '${bucketName}' creado exitosamente`);
    return {
      success: true,
      created: true,
      message: `Bucket '${bucketName}' creado exitosamente`,
    };
  } catch (error: unknown) {
    console.error(
      `Error inesperado al verificar/crear bucket '${bucketName}':`,
      error,
    );
    return {
      success: false,
      error,
      message: `Error inesperado: ${error.message || "Error desconocido"}`,
    };
  }
}

/**
 * Sube un archivo a Supabase Storage
 * @param bucketName Nombre del bucket donde subir el archivo
 * @param filePath Ruta del archivo en el bucket
 * @param file Archivo a subir
 * @returns Un objeto con el resultado de la operación
 */
export async function uploadFile(
  bucketName: string,
  filePath: string,
  file: File,
) {
  const supabase = createClientComponentClient();

  try {
    // Asegurarse de que el bucket existe
    const bucketResult = await ensureBucketExists(bucketName, true);
    if (!bucketResult.success) {
      // Si el error es de permisos, intentamos subir el archivo de todos modos
      if (
        bucketResult.error?.message &&
        (bucketResult.error.message.includes("permission") ||
          bucketResult.error.message.includes("security policy") ||
          bucketResult.error.message.includes("not authorized"))
      ) {
        console.warn(
          `Intentando subir archivo a pesar de problemas de permisos en bucket '${bucketName}'`,
        );
      } else {
        return bucketResult;
      }
    }

    // Subir el archivo
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: true,
      });

    if (error) {
      // Si el error es de permisos, intentamos obtener la URL pública de todos modos
      // (esto puede funcionar si el archivo ya existía)
      if (
        error.message &&
        (error.message.includes("permission") ||
          error.message.includes("security policy") ||
          error.message.includes("not authorized"))
      ) {
        console.warn(
          `Error de permisos al subir archivo. Intentando obtener URL pública de todos modos.`,
        );

        // Intentar obtener la URL pública
        const { data: urlData } = supabase.storage
          .from(bucketName)
          .getPublicUrl(filePath);

        if (urlData?.publicUrl) {
          return {
            success: true,
            data: null,
            publicUrl: urlData.publicUrl,
            message: "URL obtenida (permisos limitados)",
          };
        }
      }

      console.error(
        `Error al subir archivo a '${bucketName}/${filePath}':`,
        error,
      );
      return {
        success: false,
        error,
        message: `Error al subir archivo: ${error.message}`,
      };
    }

    // Obtener la URL pública
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(filePath);

    if (!urlData || !urlData.publicUrl) {
      return {
        success: false,
        message: "No se pudo obtener la URL pública del archivo",
      };
    }

    return {
      success: true,
      data,
      publicUrl: urlData.publicUrl,
      message: "Archivo subido exitosamente",
    };
  } catch (error: unknown) {
    console.error(
      `Error inesperado al subir archivo a '${bucketName}/${filePath}':`,
      error,
    );
    return {
      success: false,
      error,
      message: `Error inesperado: ${error.message || "Error desconocido"}`,
    };
  }
}

/**
 * Elimina un archivo de Supabase Storage
 * @param bucketName Nombre del bucket donde está el archivo
 * @param filePath Ruta del archivo en el bucket
 * @returns Un objeto con el resultado de la operación
 */
export async function deleteFile(bucketName: string, filePath: string) {
  const supabase = createClientComponentClient();

  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .remove([filePath]);

    if (error) {
      console.error(
        `Error al eliminar archivo '${bucketName}/${filePath}':`,
        error,
      );
      return {
        success: false,
        error,
        message: `Error al eliminar archivo: ${error.message}`,
      };
    }

    return {
      success: true,
      data,
      message: "Archivo eliminado exitosamente",
    };
  } catch (error: unknown) {
    console.error(
      `Error inesperado al eliminar archivo '${bucketName}/${filePath}':`,
      error,
    );
    return {
      success: false,
      error,
      message: `Error inesperado: ${error.message || "Error desconocido"}`,
    };
  }
}
