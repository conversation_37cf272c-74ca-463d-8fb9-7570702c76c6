import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { DefaultThemeProvider } from "@/components/theme/theme-provider";
import { ToastProvider } from "@/components/toast/toast";
import { Footer } from "@/components/ui/footer";
import { Navigation } from "@/components/ui/navigation";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "QR CURSE - Plataforma de Certificados",
  description: "Plataforma de certificados y gestión educativa para QR CURSE",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              try {
                const theme = localStorage.getItem('qr-curse-theme') || 'system';
                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                const effectiveTheme = theme === 'system' ? systemTheme : theme;
                if (effectiveTheme === 'dark') {
                  document.documentElement.classList.add('dark');
                } else {
                  document.documentElement.classList.remove('dark');
                }
                // Mark as loaded to enable transitions
                document.documentElement.classList.add('loaded');
              } catch (e) {
                // Fallback: mark as loaded even if theme detection fails
                document.documentElement.classList.add('loaded');
              }
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <DefaultThemeProvider>
          <ToastProvider>
            <Navigation />
            <main className="min-h-screen">{children}</main>
            <Footer />
          </ToastProvider>
        </DefaultThemeProvider>
      </body>
    </html>
  );
}
