import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { DefaultThemeProvider } from "@/components/theme/theme-provider";
import { ToastProvider } from "@/components/toast/toast";
import { Footer } from "@/components/ui/footer";
import { Navigation } from "@/components/ui/navigation";
import { TestTheme } from "@/components/test-theme";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "QR CURSE - Plataforma de Certificados",
  description: "Plataforma de certificados y gestión educativa para QR CURSE",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es" suppressHydrationWarning>
      <head>

      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <DefaultThemeProvider>
          <ToastProvider>
            <Navigation />
            <main className="min-h-screen">{children}</main>
            <Footer />
            <TestTheme />
          </ToastProvider>
        </DefaultThemeProvider>
      </body>
    </html>
  );
}
