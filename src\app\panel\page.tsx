"use client";

import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { PageError } from "@/components/ui/error-boundary";
import { LoadingPage } from "@/components/ui/loading";
import { supabase } from "@/lib/supabase";

// Import dashboard components
import AdminDashboard from "./components/AdminDashboard";
import InstructorDashboard from "./components/InstructorDashboard";
import StudentDashboard from "./components/StudentDashboard";

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

export default function UnifiedPanelPage() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const fetchUserData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const {
        data: { user: authUser },
        error: authError,
      } = await supabase.auth.getUser();

      if (authError || !authUser) {
        router.push("/login");
        return;
      }

      const { data: profile, error: profileError } = await supabase
        .from("users")
        .select("id, email, first_name, last_name, role")
        .eq("id", authUser.id)
        .single();

      if (profileError) {
        console.error("Error fetching user profile:", profileError);
        setError("Error al cargar el perfil de usuario");
        return;
      }

      setUser(profile);
    } catch (error) {
      console.error("Error in fetchUserData:", error);
      setError("Error inesperado al cargar los datos");
    } finally {
      setLoading(false);
    }
  }, [router]);

  useEffect(() => {
    fetchUserData();
  }, [fetchUserData]);

  if (loading) {
    return <LoadingPage />;
  }

  if (error) {
    return <PageError message={error} onRetry={fetchUserData} />;
  }

  if (!user) {
    return null; // Se redirigirá al login
  }

  // Renderizar el dashboard apropiado según el rol
  const renderDashboard = () => {
    switch (user.role.toLowerCase()) {
      case "admin":
        return <AdminDashboard user={user} />;
      case "student":
        return <StudentDashboard user={user} />;
      case "instructor":
        return <InstructorDashboard user={user} />;
      default:
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-foreground mb-4">
              Rol no reconocido
            </h2>
            <p className="text-muted-foreground">
              Tu rol ({user.role}) no está configurado en el sistema.
            </p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-border pb-4">
        <h1 className="text-3xl font-bold text-foreground">
          {user.role.toLowerCase() === "admin" && "Panel de Administración"}
          {user.role.toLowerCase() === "student" && "Mi Panel de Alumno"}
          {user.role.toLowerCase() === "instructor" && "Panel de Instructor"}
        </h1>
        <p className="text-muted-foreground mt-1">
          Bienvenido,{" "}
          {user.first_name && user.last_name
            ? `${user.first_name} ${user.last_name}`
            : user.email}
        </p>
      </div>

      {/* Dashboard Content */}
      {renderDashboard()}
    </div>
  );
}
