/**
 * @fileoverview Modern Dashboard Sidebar Component (2025 UI/UX)
 *
 * Componente de navegación lateral moderno para dashboards de QR CURSE.
 * Incluye micro-interacciones, glassmorphism, navegación colapsible y diseño responsive.
 * Optimizado para tendencias UI/UX 2025.
 */

"use client";

import React from "react";
import {
  Award,
  BarChart3,
  BookOpen,
  Building,
  ChevronLeft,
  ChevronRight,
  FileText,
  HelpCircle,
  Home,
  Settings,
  Users,
  X,
  Zap,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface DashboardSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  userRole?: "admin" | "instructor" | "student";
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  roles?: ("admin" | "instructor" | "student")[];
  badge?: string;
  isNew?: boolean;
  description?: string;
}

/**
 * Elementos de navegación principal organizados por rol con mejoras 2025
 */
const navigationItems: NavigationItem[] = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: Home,
    roles: ["admin", "instructor", "student"],
    description: "Vista general del sistema",
  },
  {
    href: "/certificates",
    label: "Certificados",
    icon: Award,
    roles: ["admin", "instructor", "student"],
    description: "Gestión de certificados",
  },
  {
    href: "/users",
    label: "Usuarios",
    icon: Users,
    roles: ["admin"],
    description: "Administrar usuarios",
  },
  {
    href: "/courses",
    label: "Cursos",
    icon: BookOpen,
    roles: ["admin", "instructor"],
    description: "Gestión de cursos",
  },
  {
    href: "/companies",
    label: "Empresas",
    icon: Building,
    roles: ["admin"],
    description: "Gestión de empresas",
  },
  {
    href: "/reports",
    label: "Reportes",
    icon: BarChart3,
    roles: ["admin", "instructor"],
    description: "Análisis y reportes",
    isNew: true,
  },
  {
    href: "/templates",
    label: "Plantillas",
    icon: FileText,
    roles: ["admin"],
    description: "Plantillas de certificados",
  },
];

/**
 * Elementos de navegación secundaria (configuración y ayuda)
 */
const secondaryItems: NavigationItem[] = [
  {
    href: "/settings",
    label: "Configuración",
    icon: Settings,
    roles: ["admin", "instructor", "student"],
    description: "Configuración del sistema",
  },
  {
    href: "/help",
    label: "Ayuda",
    icon: HelpCircle,
    roles: ["admin", "instructor", "student"],
    description: "Centro de ayuda",
  },
];

/**
 * Componente de sidebar moderno con diseño 2025
 */
export function DashboardSidebar({
  isOpen = false,
  onClose,
  userRole = "student",
  isCollapsed = false,
  onToggleCollapse,
}: DashboardSidebarProps) {
  const pathname = usePathname();
  const [hoveredItem, setHoveredItem] = React.useState<string | null>(null);

  // Filtrar elementos de navegación según el rol del usuario
  const filteredNavItems = navigationItems.filter(
    (item) => !item.roles || item.roles.includes(userRole),
  );

  const filteredSecondaryItems = secondaryItems.filter(
    (item) => !item.roles || item.roles.includes(userRole),
  );

  const sidebarWidth = isCollapsed ? "w-16" : "w-64";

  return (
    <TooltipProvider>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm lg:hidden animate-fade-in-up"
          onClick={onClose}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed left-0 top-14 z-50 h-[calc(100vh-3.5rem)] transform border-r transition-all duration-300 ease-in-out lg:translate-x-0",
          "bg-sidebar/95 backdrop-blur-md border-sidebar-border",
          sidebarWidth,
          isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
          "glass-subtle",
        )}
        aria-label="Navegación principal"
      >
        <div className="flex h-full flex-col">
          {/* Header del sidebar */}
          <div className="flex items-center justify-between p-4 border-b border-sidebar-border">
            {!isCollapsed && (
              <div className="flex items-center space-x-2 animate-fade-in-up">
                <Zap className="h-5 w-5 text-primary" />
                <span className="font-bold text-sidebar-foreground">
                  QR CURSE
                </span>
              </div>
            )}

            {/* Collapse toggle for desktop */}
            <div className="hidden lg:flex">
              {onToggleCollapse && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggleCollapse}
                  className="h-8 w-8 p-0 hover:bg-sidebar-accent"
                  aria-label={
                    isCollapsed ? "Expandir sidebar" : "Colapsar sidebar"
                  }
                >
                  {isCollapsed ? (
                    <ChevronRight className="h-4 w-4" />
                  ) : (
                    <ChevronLeft className="h-4 w-4" />
                  )}
                </Button>
              )}
            </div>

            {/* Close button for mobile */}
            <div className="lg:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0"
                aria-label="Cerrar menú de navegación"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <ScrollArea className="flex-1 px-2">
            {/* Navegación principal */}
            <div className="space-y-2 py-4">
              {!isCollapsed && (
                <div className="px-3 py-2">
                  <h2 className="mb-3 px-2 text-xs font-semibold tracking-wider uppercase text-sidebar-foreground/70">
                    Principal
                  </h2>
                </div>
              )}

              <div className="space-y-1">
                {filteredNavItems.map((item, index) => (
                  <ModernSidebarItem
                    key={item.href}
                    href={item.href}
                    icon={item.icon}
                    label={item.label}
                    description={item.description}
                    isActive={pathname === item.href}
                    isCollapsed={isCollapsed}
                    badge={item.badge}
                    isNew={item.isNew}
                    onClick={onClose}
                    onHover={setHoveredItem}
                    isHovered={hoveredItem === item.href}
                    animationDelay={index * 50}
                  />
                ))}
              </div>

              <Separator className="my-4 bg-sidebar-border" />

              {/* Navegación secundaria */}
              {!isCollapsed && (
                <div className="px-3 py-2">
                  <h2 className="mb-3 px-2 text-xs font-semibold tracking-wider uppercase text-sidebar-foreground/70">
                    Configuración
                  </h2>
                </div>
              )}

              <div className="space-y-1">
                {filteredSecondaryItems.map((item, index) => (
                  <ModernSidebarItem
                    key={item.href}
                    href={item.href}
                    icon={item.icon}
                    label={item.label}
                    description={item.description}
                    isActive={pathname === item.href}
                    isCollapsed={isCollapsed}
                    onClick={onClose}
                    onHover={setHoveredItem}
                    isHovered={hoveredItem === item.href}
                    animationDelay={(filteredNavItems.length + index) * 50}
                  />
                ))}
              </div>
            </div>
          </ScrollArea>

          {/* Footer del sidebar */}
          {!isCollapsed && (
            <div className="border-t border-sidebar-border p-4 animate-fade-in-up">
              <div className="text-xs text-sidebar-foreground/60">
                <p className="font-medium">QR CURSE v1.0</p>
                <p>© 2024 Todos los derechos reservados</p>
              </div>
            </div>
          )}
        </div>
      </aside>
    </TooltipProvider>
  );
}

/**
 * Componente moderno de elemento de navegación con micro-interacciones
 */
interface ModernSidebarItemProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  description?: string;
  isActive?: boolean;
  isCollapsed?: boolean;
  badge?: string;
  isNew?: boolean;
  onClick?: () => void;
  onHover?: (href: string | null) => void;
  isHovered?: boolean;
  animationDelay?: number;
}

function ModernSidebarItem({
  href,
  icon: Icon,
  label,
  description,
  isActive = false,
  isCollapsed = false,
  badge,
  isNew,
  onClick,
  onHover,
  isHovered,
  animationDelay = 0,
}: ModernSidebarItemProps) {
  const itemContent = (
    <Link
      href={href}
      onClick={onClick}
      onMouseEnter={() => onHover?.(href)}
      onMouseLeave={() => onHover?.(null)}
      className={cn(
        "group relative flex items-center gap-3 rounded-xl px-3 py-3 text-sm transition-all duration-200 ease-in-out",
        "hover:scale-[1.02] active:scale-[0.98]",
        isActive
          ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-lg"
          : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
        isCollapsed ? "justify-center" : "",
        "animate-fade-in-up",
      )}
      style={{
        animationDelay: `${animationDelay}ms`,
      }}
    >
      {/* Indicador de estado activo */}
      {isActive && (
        <div className="absolute left-0 top-1/2 h-6 w-1 -translate-y-1/2 rounded-r-full bg-sidebar-primary-foreground" />
      )}

      {/* Icono con animación */}
      <div
        className={cn(
          "relative flex items-center justify-center transition-transform duration-200",
          isHovered && !isActive ? "scale-110" : "",
          isActive
            ? "text-sidebar-primary-foreground"
            : "text-sidebar-foreground",
        )}
      >
        <Icon className="h-5 w-5" />

        {/* Efecto de pulso para elementos nuevos */}
        {isNew && (
          <div className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-warning animate-pulse-subtle" />
        )}
      </div>

      {/* Contenido del texto */}
      {!isCollapsed && (
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <span className="font-medium truncate">{label}</span>
            <div className="flex items-center space-x-1">
              {badge && (
                <span className="flex h-5 w-5 shrink-0 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                  {badge}
                </span>
              )}
              {isNew && (
                <span className="px-1.5 py-0.5 text-xs font-medium bg-warning text-warning-foreground rounded-md">
                  Nuevo
                </span>
              )}
            </div>
          </div>
          {description && (
            <p className="text-xs text-sidebar-foreground/60 truncate mt-0.5">
              {description}
            </p>
          )}
        </div>
      )}

      {/* Efecto de hover */}
      <div
        className={cn(
          "absolute inset-0 rounded-xl bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 transition-opacity duration-200",
          isHovered && !isActive ? "opacity-100" : "",
        )}
      />
    </Link>
  );

  // Si está colapsado, envolver en tooltip
  if (isCollapsed) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>{itemContent}</TooltipTrigger>
        <TooltipContent side="right" className="ml-2">
          <div>
            <p className="font-medium">{label}</p>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    );
  }

  return itemContent;
}

/**
 * Hook moderno para gestionar el estado del sidebar con persistencia
 */
export function useSidebarState() {
  const [isOpen, setIsOpen] = React.useState(false);
  const [isCollapsed, setIsCollapsed] = React.useState(false);

  // Persistir estado de colapso en localStorage
  React.useEffect(() => {
    const saved = localStorage.getItem("sidebar-collapsed");
    if (saved !== null) {
      setIsCollapsed(JSON.parse(saved));
    }
  }, []);

  React.useEffect(() => {
    localStorage.setItem("sidebar-collapsed", JSON.stringify(isCollapsed));
  }, [isCollapsed]);

  // Cerrar sidebar en móvil cuando cambie la ruta
  const _pathname = usePathname();
  React.useEffect(() => {
    setIsOpen(false);
  }, []);

  return {
    isOpen,
    setIsOpen,
    isCollapsed,
    setIsCollapsed,
    toggle: () => setIsOpen(!isOpen),
    close: () => setIsOpen(false),
    open: () => setIsOpen(true),
    toggleCollapse: () => setIsCollapsed(!isCollapsed),
    collapse: () => setIsCollapsed(true),
    expand: () => setIsCollapsed(false),
  };
}

/**
 * Componente de sidebar compacto moderno para espacios reducidos
 */
export function CompactSidebar({
  userRole = "student",
}: {
  userRole?: "admin" | "instructor" | "student";
}) {
  const pathname = usePathname();
  const [hoveredItem, setHoveredItem] = React.useState<string | null>(null);

  const filteredNavItems = navigationItems
    .filter((item) => !item.roles || item.roles.includes(userRole))
    .slice(0, 6); // Mostrar los primeros 6 elementos

  return (
    <TooltipProvider>
      <aside className="fixed left-0 top-14 z-40 h-[calc(100vh-3.5rem)] w-16 border-r bg-sidebar/95 backdrop-blur-md border-sidebar-border glass-subtle">
        <div className="flex h-full flex-col items-center py-4 space-y-2">
          {filteredNavItems.map((item, index) => (
            <Tooltip key={item.href}>
              <TooltipTrigger asChild>
                <Link
                  href={item.href}
                  onMouseEnter={() => setHoveredItem(item.href)}
                  onMouseLeave={() => setHoveredItem(null)}
                  className={cn(
                    "group relative flex h-12 w-12 items-center justify-center rounded-xl transition-all duration-200 ease-in-out",
                    "hover:scale-110 active:scale-95",
                    pathname === item.href
                      ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-lg"
                      : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                    "animate-fade-in-up",
                  )}
                  style={{
                    animationDelay: `${index * 100}ms`,
                  }}
                >
                  {/* Indicador de estado activo */}
                  {pathname === item.href && (
                    <div className="absolute left-0 top-1/2 h-6 w-1 -translate-y-1/2 rounded-r-full bg-sidebar-primary-foreground" />
                  )}

                  {/* Icono con efectos */}
                  <div className="relative">
                    <item.icon className="h-5 w-5" />

                    {/* Efecto de pulso para elementos nuevos */}
                    {item.isNew && (
                      <div className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-warning animate-pulse-subtle" />
                    )}
                  </div>

                  {/* Efecto de hover */}
                  <div
                    className={cn(
                      "absolute inset-0 rounded-xl bg-gradient-to-r from-primary/10 to-secondary/10 opacity-0 transition-opacity duration-200",
                      hoveredItem === item.href && pathname !== item.href
                        ? "opacity-100"
                        : "",
                    )}
                  />
                </Link>
              </TooltipTrigger>
              <TooltipContent side="right" className="ml-2">
                <div>
                  <p className="font-medium">{item.label}</p>
                  {item.description && (
                    <p className="text-xs text-muted-foreground">
                      {item.description}
                    </p>
                  )}
                </div>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>
      </aside>
    </TooltipProvider>
  );
}
