"use client";

import {
  Award,
  Bar<PERSON>hart3,
  BookOpen,
  Calendar,
  Plus,
  Settings,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import DatabaseStatus from "@/app/components/DatabaseStatus";
import { AnimateIn, StaggeredList } from "@/components/ui/animations";
import { PageError } from "@/components/ui/error-boundary";
import { LoadingPage } from "@/components/ui/loading";
import { ModernButton } from "@/components/ui/modern-button";
import {
  ModernCard,
  ModernCardContent,
  StatCard,
} from "@/components/ui/modern-card";
import { ensureValidSession, supabase } from "@/lib/supabase";

// Tipos para mejorar la seguridad de tipos
interface DashboardStats {
  totalStudents: number;
  totalCertificates: number;
  totalCourses: number;
  totalInstructors: number;
  recentActivity: RecentActivity[];
  monthlyGrowth: {
    students: number;
    certificates: number;
    courses: number;
  };
}

interface RecentActivity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalStudents: 0,
    totalCertificates: 0,
    totalCourses: 0,
    totalInstructors: 0,
    recentActivity: [],
    monthlyGrowth: {
      students: 0,
      certificates: 0,
      courses: 0,
    },
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Verificar si la sesión está activa y válida
      const isSessionValid = await ensureValidSession();
      if (!isSessionValid) {
        throw new Error(
          "Sesión no válida. Por favor, inicia sesión nuevamente.",
        );
      }

      // Obtener conteos de las tablas principales usando Promise.allSettled para manejar errores individuales
      const [
        studentsResult,
        certificatesResult,
        coursesResult,
        instructorsResult,
      ] = await Promise.allSettled([
        supabase
          .from("users")
          .select("*", { count: "exact", head: true })
          .eq("role", "student"),
        supabase
          .from("certificates")
          .select("*", { count: "exact", head: true }),
        supabase.from("courses").select("*", { count: "exact", head: true }),
        supabase
          .from("users")
          .select("*", { count: "exact", head: true })
          .in("role", ["admin", "instructor"]),
      ]);

      const studentCount =
        studentsResult.status === "fulfilled"
          ? studentsResult.value.count || 0
          : 0;
      const certificateCount =
        certificatesResult.status === "fulfilled"
          ? certificatesResult.value.count || 0
          : 0;
      const courseCount =
        coursesResult.status === "fulfilled"
          ? coursesResult.value.count || 0
          : 0;
      const instructorCount =
        instructorsResult.status === "fulfilled"
          ? instructorsResult.value.count || 0
          : 0;

      setStats({
        totalStudents: studentCount,
        totalCertificates: certificateCount,
        totalCourses: courseCount,
        totalInstructors: instructorCount,
        recentActivity: [], // Por ahora vacío, se puede implementar después
        monthlyGrowth: {
          students: Math.floor(Math.random() * 20), // Datos simulados por ahora
          certificates: Math.floor(Math.random() * 15),
          courses: Math.floor(Math.random() * 5),
        },
      });
    } catch (err) {
      console.error("Error loading dashboard data:", err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Mostrar loading mientras se cargan los datos
  if (loading) {
    return <LoadingPage message="Cargando dashboard..." />;
  }

  // Mostrar error si ocurre algún problema
  if (error) {
    return (
      <PageError
        title="Error al cargar el dashboard"
        message={error.message}
        onRetry={fetchDashboardData}
      />
    );
  }

  // Si no hay datos, mostrar mensaje
  if (!stats) {
    return <LoadingPage message="Inicializando dashboard..." />;
  }

  // Datos para las métricas (ahora usando stats del estado)
  const metrics = [
    {
      id: "students",
      title: "Estudiantes",
      value: stats.totalStudents.toString(),
      description: "Usuarios registrados",
      icon: Users,
      trend: "up" as "up",
      trendValue: `+${stats.monthlyGrowth.students}%`,
      color: "blue",
    },
    {
      id: "certificates",
      title: "Certificados",
      value: stats.totalCertificates.toString(),
      description: "Certificados emitidos",
      icon: Award,
      trend: "up" as "up",
      trendValue: `+${stats.monthlyGrowth.certificates}%`,
      color: "green",
    },
    {
      id: "courses",
      title: "Cursos",
      value: stats.totalCourses.toString(),
      description: "Cursos disponibles",
      icon: BookOpen,
      trend: (stats.monthlyGrowth.courses > 0 ? "up" : "neutral") as
        | "up"
        | "neutral",
      trendValue:
        stats.monthlyGrowth.courses > 0
          ? `+${stats.monthlyGrowth.courses}%`
          : undefined,
      color: "purple",
    },
    {
      id: "instructors",
      title: "Instructores",
      value: stats.totalInstructors.toString(),
      description: "Instructores activos",
      icon: TrendingUp,
      trend: "neutral" as "neutral",
      color: "orange",
    },
  ];

  // Acciones rápidas
  const quickActions = [
    {
      id: "new-certificate",
      title: "Nuevo Certificado",
      description: "Crear un certificado para un estudiante",
      icon: Award,
      href: "/panel-admin/certificados/nuevo",
      color: "blue",
    },
    {
      id: "manage-students",
      title: "Gestionar Alumnos",
      description: "Ver y administrar estudiantes",
      icon: Users,
      href: "/panel-admin/alumnos",
      color: "green",
    },
    {
      id: "create-course",
      title: "Crear Curso",
      description: "Agregar un nuevo curso",
      icon: BookOpen,
      href: "/panel-admin/cursos/nuevo",
      color: "purple",
    },
    {
      id: "settings",
      title: "Configuración",
      description: "Ajustes del sistema",
      icon: Settings,
      href: "/panel-admin/configuracion",
      color: "gray",
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <AnimateIn animation="fade" duration="normal">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
            <p className="mt-1 text-muted-foreground">
              Vista general del sistema QR CURSE
            </p>
          </div>
          <ModernButton variant="gradient" size="lg" asChild>
            <Link
              href="/panel-admin/certificados/nuevo"
              className="flex items-center gap-2"
            >
              <span className="flex items-center gap-2">
                <Plus className="w-5 h-5" />
                Nuevo Certificado
              </span>
            </Link>
          </ModernButton>
        </div>
      </AnimateIn>

      {/* Área principal con fondo sutil y padding */}
      <div className="w-full min-h-screen bg-muted/50 px-4 md:px-8 py-10">
        {/* Resumen General - Métricas principales en grid horizontal */}
        <AnimateIn animation="fade" duration="fast">
          <section className="mb-14 max-w-6xl mx-auto w-full">
            <h2 className="text-xl font-semibold text-foreground mb-8 flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-primary" />
              Resumen General
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {metrics.map((metric) => (
                <StatCard
                  key={metric.id}
                  title={metric.title}
                  value={metric.value}
                  description={metric.description}
                  icon={metric.icon}
                  trend={metric.trend}
                  trendValue={metric.trendValue}
                  variant="glass"
                />
              ))}
            </div>
          </section>
        </AnimateIn>

        {/* Acciones Rápidas - grid horizontal debajo de métricas */}
        <AnimateIn animation="slide-up" duration="fast">
          <section className="mb-14 max-w-6xl mx-auto w-full">
            <h2 className="text-xl font-semibold text-foreground mb-8 flex items-center gap-2">
              <Zap className="w-5 h-5 text-primary" />
              Acciones Rápidas
            </h2>
            <ModernCard variant="glass" className="h-full">
              <ModernCardContent className="p-8">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {quickActions.map((action) => (
                    <Link key={action.id} href={action.href}>
                      <ModernCard
                        variant="glass"
                        interactive
                        className="group cursor-pointer transition-transform duration-200 hover:scale-[1.04] hover:shadow-2xl"
                      >
                        <ModernCardContent className="p-6">
                          <div className="flex items-center gap-4">
                            <div
                              className={`p-3 rounded-xl bg-${action.color}-100 text-${action.color}-600 group-hover:scale-110 transition-transform`}
                            >
                              <action.icon className="w-7 h-7" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-base">
                                {action.title}
                              </h4>
                              <p className="text-xs text-muted-foreground">
                                {action.description}
                              </p>
                            </div>
                          </div>
                        </ModernCardContent>
                      </ModernCard>
                    </Link>
                  ))}
                </div>
              </ModernCardContent>
            </ModernCard>
          </section>
        </AnimateIn>

        {/* Actividad Reciente - sección separada */}
        <AnimateIn animation="slide-up" duration="normal">
          <section className="max-w-6xl mx-auto w-full">
            <h2 className="text-xl font-semibold text-foreground mb-8 flex items-center gap-2">
              <Calendar className="w-5 h-5 text-primary" />
              Actividad Reciente
            </h2>
            <ModernCard variant="glass" className="h-full">
              <ModernCardContent className="p-8">
                <div className="space-y-6">
                  {stats.recentActivity.length === 0 ? (
                    <div className="text-center py-16 text-muted-foreground">
                      <Calendar className="w-14 h-14 mx-auto mb-6 opacity-50" />
                      <h3 className="text-lg font-semibold mb-2">
                        No hay actividad reciente
                      </h3>
                      <p className="text-sm">
                        Las actividades aparecerán aquí cuando ocurran eventos
                        en el sistema
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                      {stats.recentActivity.map((activity) => (
                        <div
                          key={activity.id}
                          className="flex items-center gap-4 p-5 rounded-xl bg-muted/30 hover:bg-muted/50 transition-colors shadow"
                        >
                          <div className="w-4 h-4 rounded-full bg-primary flex-shrink-0"></div>
                          <div className="flex-1 min-w-0">
                            <p className="text-base font-medium truncate">
                              {activity.type}
                            </p>
                            <p className="text-xs text-muted-foreground truncate">
                              {activity.description}
                            </p>
                            <span className="text-xs text-muted-foreground">
                              {activity.timestamp}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </ModernCardContent>
            </ModernCard>
          </section>
        </AnimateIn>
      </div>

      {/* Estado de la base de datos */}
      <ModernCard variant="minimal">
        <ModernCardContent className="p-4">
          <DatabaseStatus />
        </ModernCardContent>
      </ModernCard>
    </div>
  );
}
