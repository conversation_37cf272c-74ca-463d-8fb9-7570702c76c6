"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { ensureValidSession, supabase, type User } from "@/lib/supabase";

// Define interfaces for better type safety
interface Course {
  id: string;
  title: string | null;
}

interface Certificate {
  id: string;
  issue_date: string;
  status: string;
  course_id: string | null;
  certificate_number?: string | null;
  course?: Course | null;
}

export default function StudentDashboard() {
  const [userData, setUserData] = useState<User | null>(null);

  const [certificatesCount, setCertificatesCount] = useState(0);
  const [activeCertificatesCount, setActiveCertificatesCount] = useState(0);
  const [recentCertificates, setRecentCertificates] = useState<Certificate[]>(
    [],
  ); // Use Certificate type
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        // Verificar si la sesión está activa y válida
        const isSessionValid = await ensureValidSession();

        if (!isSessionValid) {
          throw new Error("No hay sesión activa o ha expirado");
        }

        // Get current user
        const {
          data: { user },
          error: userAuthError,
        } = await supabase.auth.getUser();

        if (userAuthError) {
          console.error("Error fetching user auth data:", userAuthError);
          throw userAuthError;
        }

        if (!user) {
          throw new Error("No se pudo obtener información del usuario");
        }

        // Get user details
        const { data: userDataFromDB, error: userError } = await supabase
          .from("users")
          .select("id, first_name, last_name, email, role")
          .eq("id", user.id)
          .single();

        if (userError) {
          console.error("Error fetching user data:", userError);
          throw userError;
        }

        setUserData(userDataFromDB as User);

        // Get certificates count
        const { count: totalCount, error: countError } = await supabase
          .from("certificates")
          .select("id", { count: "exact", head: true })
          .eq("user_id", user.id);

        if (countError) throw countError;
        setCertificatesCount(totalCount || 0);

        // Get active certificates count
        const { count: activeCount, error: activeCountError } = await supabase
          .from("certificates")
          .select("id", { count: "exact", head: true })
          .eq("user_id", user.id)
          .eq("status", "ACTIVE");

        if (activeCountError) throw activeCountError;
        setActiveCertificatesCount(activeCount || 0);

        // Get recent certificates with course information
        // Use a direct query without relying on relationships
        const { data: recentCertsData, error: recentError } = await supabase
          .from("certificates")
          .select(`
            id,
            issue_date,
            status,
            course_id
          `)
          .eq("user_id", user.id)
          .order("issue_date", { ascending: false })
          .limit(3);

        if (recentError) throw recentError;

        // Fetch course data separately if we have certificates
        let certificatesWithCourses: Certificate[] = []; // Explicitly type the array
        if (recentCertsData && recentCertsData.length > 0) {
          // Get all course IDs from certificates
          const courseIds = recentCertsData
            .map((cert) => cert.course_id)
            .filter(Boolean);

          if (courseIds.length > 0) {
            // Fetch courses data
            const { data: coursesData } = await supabase
              .from("courses")
              .select("id, title")
              .in("id", courseIds);

            // Map courses to certificates
            certificatesWithCourses = recentCertsData.map((cert) => {
              const course =
                coursesData?.find((c) => c.id === cert.course_id) || null;
              return {
                ...cert,
                course,
              };
            });
          } else {
            certificatesWithCourses = recentCertsData;
          }
        }

        setRecentCertificates(certificatesWithCourses || []);
      } catch (error: unknown) {
        console.error("Error fetching user data:", error);

        // Mejorar el manejo de errores para mostrar mensajes más específicos
        if (error instanceof Error) {
          if (
            error.message.includes("No hay sesión") ||
            error.message.includes("session")
          ) {
            setError(
              "Tu sesión ha expirado. Por favor, vuelve a iniciar sesión.",
            );
          } else if (
            error.message.includes("network") ||
            error.message.includes("connection")
          ) {
            setError("Error de conexión. Verifica tu conexión a internet.");
          } else if (
            error.message.includes("status of 400") ||
            error.message.includes("load resource")
          ) {
            setError(
              "Error al cargar los recursos. Por favor, recarga la página.",
            );
          } else {
            setError(error.message || "Error al cargar los datos");
          }
        } else {
          setError("Ocurrió un error desconocido al cargar los datos");
        }
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-gray-500">Cargando datos...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4">
        <div className="flex">
          <div className="ml-3">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Welcome Message */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">
          ¡Bienvenido,{" "}
          {userData?.first_name && userData?.last_name
            ? `${userData.first_name} ${userData.last_name}`
            : userData?.email || "Usuario"}
          !
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Este es tu panel de estudiante donde puedes ver tus certificados y
          notas.
        </p>
        {/* Conditionally show profile update prompt */}
        {(!userData?.first_name || !userData?.last_name) && (
          <p className="mt-2 text-sm text-gray-600">
            Por favor,{" "}
            <Link
              href="/panel-alumno/perfil"
              className="font-medium text-indigo-600 hover:text-indigo-500"
            >
              actualiza tus datos de perfil
            </Link>{" "}
            para mantener tu información al día.
          </p>
        )}
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5 mb-8">
        {/* Total Certificates */}
        <div className="bg-gray-100 overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-gray-700 rounded-md p-3">
                <svg
                  className="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-700 truncate">
                    Total de Certificados
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {certificatesCount}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-200 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link
                href="/panel-alumno/certificados"
                className="font-medium text-gray-700 hover:text-gray-900"
              >
                Ver todos mis certificados{" "}
                <span aria-hidden="true">&rarr;</span>
              </Link>
            </div>
          </div>
        </div>

        {/* Active Certificates */}
        <div className="bg-gray-100 overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-gray-700 rounded-md p-3">
                <svg
                  className="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-700 truncate">
                    Certificados Activos
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {activeCertificatesCount}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-200 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link
                href="/panel-alumno/certificados"
                className="font-medium text-gray-700 hover:text-gray-900"
              >
                Ver certificados <span aria-hidden="true">&rarr;</span>
              </Link>
            </div>
          </div>
        </div>

        {/* Next Assignments */}
        <div className="bg-gray-100 overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-gray-700 rounded-md p-3">
                <svg
                  className="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-700 truncate">
                    Próximas Evaluaciones
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      0
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-200 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link
                href="/panel-alumno/mis-notas"
                className="font-medium text-gray-700 hover:text-gray-900"
              >
                Ver evaluaciones <span aria-hidden="true">&rarr;</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Certificates */}
      <div className="mb-8">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Certificados Recientes
        </h2>

        {recentCertificates.length === 0 ? (
          <div className="rounded-md bg-blue-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-blue-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3 flex-1 md:flex md:justify-between">
                <p className="text-sm text-blue-700">
                  Aún no tienes certificados emitidos.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {recentCertificates.map((certificate) => (
                <li key={certificate.id}>
                  <Link
                    href={`/panel-alumno/certificados/${certificate.id}`}
                    className="block hover:bg-gray-50"
                  >
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-700 truncate">
                          {certificate.course?.title || "Curso sin nombre"}
                        </p>
                        <div className="ml-2 flex-shrink-0 flex">
                          <p
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              certificate.status === "ACTIVE" ||
                              certificate.status === "active"
                                ? "bg-gray-300 text-gray-800"
                                : certificate.status === "REVOKED" ||
                                    certificate.status === "revoked"
                                  ? "bg-gray-400 text-gray-800"
                                  : "bg-gray-200 text-gray-800"
                            }`}
                          >
                            {certificate.status === "ACTIVE" ||
                            certificate.status === "active"
                              ? "Activo"
                              : certificate.status === "REVOKED" ||
                                  certificate.status === "revoked"
                                ? "Revocado"
                                : "Expirado"}
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 sm:flex sm:justify-between">
                        <div className="sm:flex">
                          <p className="flex items-center text-sm text-gray-500">
                            {certificate.certificate_number
                              ? `Certificado N° ${certificate.certificate_number}`
                              : `Certificado ID: ${certificate.id.substring(0, 8)}...`}
                          </p>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                          <p>
                            Emitido el:{" "}
                            {new Date(
                              certificate.issue_date,
                            ).toLocaleDateString("es-ES")}
                          </p>
                        </div>
                      </div>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
            {certificatesCount > 3 && (
              <div className="bg-gray-200 px-4 py-3 text-right sm:px-6">
                <Link
                  href="/panel-alumno/certificados"
                  className="text-sm font-medium text-gray-700 hover:text-gray-900"
                >
                  Ver todos los certificados <span aria-hidden="true">→</span>
                </Link>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Acciones Rápidas
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div className="relative rounded-lg border border-gray-300 bg-gray-100 px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-gray-600">
            <div className="flex-shrink-0">
              <svg
                className="h-10 w-10 text-gray-700"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <Link
                href="/panel-alumno/certificados"
                className="focus:outline-none"
              >
                <span className="absolute inset-0" aria-hidden="true" />
                <p className="text-sm font-medium text-gray-900">
                  Ver Mis Certificados
                </p>
                <p className="text-sm text-gray-600">
                  Accede a todos tus certificados emitidos
                </p>
              </Link>
            </div>
          </div>
          {/* Updated Mi Perfil Card */}
          <div className="relative rounded-lg border border-gray-300 bg-gray-100 px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-gray-600">
            <div className="flex-shrink-0">
              <svg
                className="h-10 w-10 text-gray-700"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                ></path>
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <Link href="/panel-alumno/perfil" className="focus:outline-none">
                <span className="absolute inset-0" aria-hidden="true"></span>
                <p className="text-sm font-medium text-gray-900">Mi Perfil</p>
                <p className="text-sm text-gray-600">
                  Actualiza tu información personal
                </p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
