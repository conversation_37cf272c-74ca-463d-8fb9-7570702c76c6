"use client";

import {
  Award,
  Bar<PERSON>hart3,
  BookOpen,
  Calendar,
  RefreshCw,
  TrendingUp,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";
import { Charts } from "@/components/reports/charts";
import { Filters } from "@/components/reports/filters";
import { MetricsCards } from "@/components/reports/metrics-cards";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  type ReportFilters,
  type ReportsData,
  reportsService,
} from "@/lib/services/reports-service";

export default function ReportsPage() {
  const [reportsData, setReportsData] = useState<ReportsData | null>(null);
  const [filters, setFilters] = useState<ReportFilters>({});
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Load reports data
  const loadReportsData = async (newFilters: ReportFilters = filters) => {
    setIsLoading(true);
    try {
      const data = await reportsService.getReportsData(newFilters);
      setReportsData(data);
      setLastUpdated(new Date());
    } catch (error) {
      console.error("Error loading reports data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadReportsData();
  }, [loadReportsData]);

  // Handle filter changes
  const handleFiltersChange = (newFilters: ReportFilters) => {
    setFilters(newFilters);
    loadReportsData(newFilters);
  };

  // Handle export
  const handleExport = async (format: "pdf" | "excel") => {
    if (!reportsData) return;

    try {
      // TODO: Implement actual export functionality
      console.log(`Exporting reports in ${format} format...`);

      if (format === "excel") {
        // Mock Excel export
        const csvData = await reportsService.exportToCSV(
          reportsData.coursePerformance,
          "course-performance",
        );

        // Create and download file
        const blob = new Blob([csvData], { type: "text/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `reportes-${new Date().toISOString().split("T")[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      } else {
        // Mock PDF export
        alert("Funcionalidad de exportación a PDF en desarrollo");
      }
    } catch (error) {
      console.error(`Error exporting to ${format}:`, error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <BarChart3 className="h-8 w-8 text-primary" />
            Reportes y Análisis
          </h1>
          <p className="text-muted-foreground mt-1">
            Dashboard completo de métricas y estadísticas de la plataforma
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            Actualizado: {lastUpdated.toLocaleTimeString("es-ES")}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadReportsData()}
            disabled={isLoading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-1 ${isLoading ? "animate-spin" : ""}`}
            />
            Actualizar
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Filters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onExport={handleExport}
        isLoading={isLoading}
      />

      {/* Quick Stats */}
      {reportsData && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 border-blue-200 dark:border-blue-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    Total Certificados
                  </p>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {reportsData.metrics.totalCertificates.toLocaleString()}
                  </p>
                </div>
                <Award className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 border-green-200 dark:border-green-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-700 dark:text-green-300">
                    Usuarios Activos
                  </p>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {reportsData.metrics.activeUsers.toLocaleString()}
                  </p>
                </div>
                <Users className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 border-purple-200 dark:border-purple-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-700 dark:text-purple-300">
                    Cursos Completados
                  </p>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {reportsData.metrics.completedCourses.toLocaleString()}
                  </p>
                </div>
                <BookOpen className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 border-orange-200 dark:border-orange-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-700 dark:text-orange-300">
                    Tasa de Éxito
                  </p>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {(
                      (reportsData.metrics.activeCertificates /
                        reportsData.metrics.totalCertificates) *
                      100
                    ).toFixed(1)}
                    %
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Separator />

      {/* Detailed Metrics */}
      {reportsData && (
        <div>
          <h2 className="text-2xl font-semibold mb-4">Métricas Detalladas</h2>
          <MetricsCards metrics={reportsData.metrics} isLoading={isLoading} />
        </div>
      )}

      <Separator />

      {/* Charts */}
      {reportsData && (
        <div>
          <h2 className="text-2xl font-semibold mb-4">Análisis Visual</h2>
          <Charts
            certificatesByMonth={reportsData.certificatesByMonth}
            usersByRole={reportsData.usersByRole}
            coursePerformance={reportsData.coursePerformance}
            isLoading={isLoading}
          />
        </div>
      )}

      {/* Performance Tables */}
      {reportsData && (
        <div className="grid gap-6 md:grid-cols-2">
          {/* Course Performance Table */}
          <Card>
            <CardHeader>
              <CardTitle>Rendimiento de Cursos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {reportsData.coursePerformance.map((course) => (
                  <div
                    key={course.courseId}
                    className="flex items-center justify-between p-3 rounded-lg bg-muted/50"
                  >
                    <div>
                      <p className="font-medium">{course.courseName}</p>
                      <p className="text-sm text-muted-foreground">
                        {course.totalEnrollments} inscripciones
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge
                        variant={
                          course.completionRate > 90 ? "default" : "secondary"
                        }
                      >
                        {course.completionRate.toFixed(1)}%
                      </Badge>
                      <p className="text-sm text-muted-foreground mt-1">
                        Nota: {course.averageGrade.toFixed(1)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Instructor Stats Table */}
          <Card>
            <CardHeader>
              <CardTitle>Estadísticas de Instructores</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {reportsData.instructorStats.map((instructor) => (
                  <div
                    key={instructor.instructorId}
                    className="flex items-center justify-between p-3 rounded-lg bg-muted/50"
                  >
                    <div>
                      <p className="font-medium">{instructor.instructorName}</p>
                      <p className="text-sm text-muted-foreground">
                        {instructor.totalCourses} cursos •{" "}
                        {instructor.totalStudents} estudiantes
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline">
                        ⭐ {instructor.averageRating.toFixed(1)}
                      </Badge>
                      <p className="text-sm text-muted-foreground mt-1">
                        {instructor.certificatesIssued} certificados
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
