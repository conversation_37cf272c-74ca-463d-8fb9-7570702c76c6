/**
 * @fileoverview Dashboard Layout Component
 *
 * Layout principal para todas las páginas de dashboard de QR CURSE.
 * Incluye header, sidebar responsivo, breadcrumbs y área de contenido principal.
 */

"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { DashboardBreadcrumbs } from "./dashboard-breadcrumbs";
import { DashboardHeader, useCurrentUser } from "./dashboard-header";
import { DashboardSidebar, useSidebarState } from "./dashboard-sidebar";

interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
  }>;
  className?: string;
  showBreadcrumbs?: boolean;
  showSidebar?: boolean;
}

/**
 * Layout principal del dashboard con navegación responsiva
 */
export function DashboardLayout({
  children,
  title,
  subtitle,
  actions,
  breadcrumbs,
  className,
  showBreadcrumbs = true,
  showSidebar = true,
}: DashboardLayoutProps) {
  const user = useCurrentUser();
  const sidebar = useSidebarState();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <DashboardHeader onMenuClick={sidebar.toggle} user={user} />

      <div className="flex">
        {/* Sidebar */}
        {showSidebar && (
          <DashboardSidebar
            isOpen={sidebar.isOpen}
            onClose={sidebar.close}
            isCollapsed={sidebar.isCollapsed}
            onToggleCollapse={sidebar.toggleCollapse}
            userRole={user?.role as "admin" | "instructor" | "student"}
          />
        )}

        {/* Main content */}
        <main
          className={cn(
            "flex-1 transition-all duration-300 ease-in-out",
            showSidebar ? (sidebar.isCollapsed ? "lg:ml-16" : "lg:ml-64") : "",
            className,
          )}
          id="main-content"
        >
          <div className="container mx-auto p-6 lg:p-8">
            {/* Breadcrumbs */}
            {showBreadcrumbs && breadcrumbs && (
              <div className="mb-6">
                <DashboardBreadcrumbs items={breadcrumbs} />
              </div>
            )}

            {/* Page header */}
            {(title || actions) && (
              <div className="mb-8">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                  {title && (
                    <div>
                      <h1 className="text-2xl font-bold tracking-tight lg:text-3xl">
                        {title}
                      </h1>
                      {subtitle && (
                        <p className="text-muted-foreground mt-1 text-sm lg:text-base">
                          {subtitle}
                        </p>
                      )}
                    </div>
                  )}
                  {actions && (
                    <div className="flex flex-wrap gap-2">{actions}</div>
                  )}
                </div>
              </div>
            )}

            {/* Page content */}
            <div className="space-y-6">{children}</div>
          </div>
        </main>
      </div>
    </div>
  );
}

/**
 * Layout simplificado para páginas de dashboard sin sidebar
 */
export function SimpleDashboardLayout({
  children,
  title,
  subtitle,
  actions,
  className,
}: Omit<DashboardLayoutProps, "showSidebar" | "showBreadcrumbs">) {
  return (
    <DashboardLayout
      title={title}
      subtitle={subtitle}
      actions={actions}
      className={className}
      showSidebar={false}
      showBreadcrumbs={false}
    >
      {children}
    </DashboardLayout>
  );
}

/**
 * Layout para páginas de configuración con navegación específica
 */
export function SettingsLayout({
  children,
  title,
  subtitle,
  settingsNav,
}: {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  settingsNav?: React.ReactNode;
}) {
  const user = useCurrentUser();
  const sidebar = useSidebarState();

  return (
    <div className="min-h-screen bg-background">
      <DashboardHeader onMenuClick={sidebar.toggle} user={user} />

      <div className="flex">
        <DashboardSidebar
          isOpen={sidebar.isOpen}
          onClose={sidebar.close}
          userRole={user?.role as "admin" | "instructor" | "student"}
        />

        <main className="flex-1 lg:ml-64">
          <div className="container mx-auto p-6 lg:p-8">
            {/* Settings header */}
            {title && (
              <div className="mb-8">
                <h1 className="text-2xl font-bold tracking-tight lg:text-3xl">
                  {title}
                </h1>
                {subtitle && (
                  <p className="text-muted-foreground mt-1 text-sm lg:text-base">
                    {subtitle}
                  </p>
                )}
              </div>
            )}

            <div className="flex flex-col lg:flex-row gap-8">
              {/* Settings navigation */}
              {settingsNav && (
                <aside className="lg:w-64 flex-shrink-0">{settingsNav}</aside>
              )}

              {/* Settings content */}
              <div className="flex-1 min-w-0">{children}</div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

/**
 * Layout para páginas de autenticación
 */
export function AuthLayout({
  children,
  title,
  subtitle,
}: {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
}) {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      {/* Left side - Branding */}
      <div className="lg:w-1/2 bg-primary flex items-center justify-center p-8">
        <div className="text-center text-primary-foreground">
          <h1 className="text-4xl font-bold mb-4">QR CURSE</h1>
          <p className="text-xl opacity-90">
            Sistema de Gestión de Certificados
          </p>
          <p className="mt-4 opacity-75">
            Plataforma moderna para la emisión y verificación de certificados
            educativos
          </p>
        </div>
      </div>

      {/* Right side - Form */}
      <div className="lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-6">
          {title && (
            <div className="text-center">
              <h2 className="text-2xl font-bold">{title}</h2>
              {subtitle && (
                <p className="text-muted-foreground mt-2">{subtitle}</p>
              )}
            </div>
          )}
          {children}
        </div>
      </div>
    </div>
  );
}

/**
 * Layout para páginas públicas (verificación de certificados, etc.)
 */
export function PublicLayout({
  children,
  title,
  subtitle,
  showHeader = true,
}: {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showHeader?: boolean;
}) {
  return (
    <div className="min-h-screen bg-background">
      {showHeader && (
        <header className="border-b">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="font-bold text-xl">QR CURSE</span>
              </div>
              <nav className="flex items-center space-x-4">
                <Button variant="ghost" asChild>
                  <a href="/verify">Verificar Certificado</a>
                </Button>
                <Button asChild>
                  <a href="/login">Iniciar Sesión</a>
                </Button>
              </nav>
            </div>
          </div>
        </header>
      )}

      <main className="container mx-auto px-4 py-8">
        {title && (
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-2">{title}</h1>
            {subtitle && (
              <p className="text-muted-foreground text-lg">{subtitle}</p>
            )}
          </div>
        )}
        {children}
      </main>
    </div>
  );
}

/**
 * Hook para gestionar el estado del layout
 */
export function useLayoutState() {
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

  return {
    sidebarCollapsed,
    setSidebarCollapsed,
    mobileMenuOpen,
    setMobileMenuOpen,
    toggleSidebar: () => setSidebarCollapsed(!sidebarCollapsed),
    toggleMobileMenu: () => setMobileMenuOpen(!mobileMenuOpen),
    closeMobileMenu: () => setMobileMenuOpen(false),
  };
}
