"use client";

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Award, Plus, Search, Eye } from "lucide-react";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { AnimateIn, StaggeredList } from "@/components/ui/animations";
import { PageError } from "@/components/ui/error-boundary";
import { LoadingPage } from "@/components/ui/loading";
import { ModernButton } from "@/components/ui/modern-button";
import {
  ModernCard,
  ModernCardContent,
} from "@/components/ui/modern-card";

interface Certificate {
  id: string;
  certificate_number: string;
  issue_date: string;
  status: string;
  user_id: string;
  course_id: string;
  users?: {
    first_name: string;
    last_name: string;
    email: string;
  };
  courses?: {
    title: string;
  };
}

export default function CertificatesPage() {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [filteredCertificates, setFilteredCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const supabase = createClientComponentClient();

  const fetchCertificates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: certificatesData, error: certificatesError } = await supabase
        .from("certificates")
        .select(`
          id,
          certificate_number,
          issue_date,
          status,
          user_id,
          course_id,
          users (
            first_name,
            last_name,
            email
          ),
          courses (
            title
          )
        `)
        .order("issue_date", { ascending: false });

      if (certificatesError) {
        throw certificatesError;
      }

      setCertificates(certificatesData || []);
      setFilteredCertificates(certificatesData || []);
    } catch (error) {
      console.error("Error fetching certificates:", error);
      setError(error instanceof Error ? error.message : "Error desconocido");
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    fetchCertificates();
  }, [fetchCertificates]);

  // Filter certificates
  useEffect(() => {
    let filtered = certificates;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(cert =>
        cert.certificate_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.users?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.users?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.courses?.title?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter(cert => cert.status?.toLowerCase() === statusFilter);
    }

    setFilteredCertificates(filtered);
  }, [certificates, searchTerm, statusFilter]);

  if (loading) {
    return <LoadingPage />;
  }

  if (error) {
    return <PageError message={error} onRetry={fetchCertificates} />;
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "expired":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "revoked":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "Activo";
      case "expired":
        return "Expirado";
      case "revoked":
        return "Revocado";
      default:
        return status || "Desconocido";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-border pb-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Gestión de Certificados</h1>
          <p className="text-muted-foreground mt-1">
            Administra todos los certificados emitidos en el sistema
          </p>
        </div>
        <Link href="/panel/certificados/nuevo">
          <ModernButton>
            <Plus className="w-4 h-4 mr-2" />
            Nuevo Certificado
          </ModernButton>
        </Link>
      </div>

      {/* Filters */}
      <AnimateIn animation="fade" duration="normal">
        <ModernCard>
          <ModernCardContent>
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Buscar por número, alumno o curso..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="sm:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="all">Todos los estados</option>
                  <option value="active">Activos</option>
                  <option value="expired">Expirados</option>
                  <option value="revoked">Revocados</option>
                </select>
              </div>

              {/* Stats */}
              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground">{certificates.length}</div>
                  <div className="text-muted-foreground">Total</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground">
                    {certificates.filter(c => c.status?.toLowerCase() === "active").length}
                  </div>
                  <div className="text-muted-foreground">Activos</div>
                </div>
              </div>
            </div>
          </ModernCardContent>
        </ModernCard>
      </AnimateIn>

      {/* Certificates List */}
      <AnimateIn animation="slide" direction="up" delay={200}>
        {filteredCertificates.length > 0 ? (
          <StaggeredList className="space-y-4">
            {filteredCertificates.map((certificate) => (
              <ModernCard key={certificate.id}>
                <ModernCardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 flex-1 min-w-0">
                      <div className="bg-primary/10 p-3 rounded-lg">
                        <Award className="w-6 h-6 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-foreground truncate">
                          #{certificate.certificate_number}
                        </h3>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-sm text-muted-foreground">
                            {certificate.users?.first_name} {certificate.users?.last_name}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {certificate.courses?.title}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-sm text-muted-foreground">
                            {certificate.users?.email}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            Emitido: {new Date(certificate.issue_date).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <span
                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                          certificate.status
                        )}`}
                      >
                        {getStatusLabel(certificate.status)}
                      </span>
                      
                      <Link href={`/panel/certificados/${certificate.id}`}>
                        <ModernButton variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-2" />
                          Ver
                        </ModernButton>
                      </Link>
                    </div>
                  </div>
                </ModernCardContent>
              </ModernCard>
            ))}
          </StaggeredList>
        ) : (
          <ModernCard>
            <ModernCardContent>
              <div className="text-center py-12">
                <Award className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">
                  {searchTerm || statusFilter !== "all" 
                    ? "No se encontraron certificados" 
                    : "No hay certificados emitidos"}
                </h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || statusFilter !== "all"
                    ? "Intenta ajustar los filtros de búsqueda"
                    : "Comienza emitiendo el primer certificado"}
                </p>
                {(!searchTerm && statusFilter === "all") && (
                  <Link href="/panel/certificados/nuevo">
                    <ModernButton>
                      <Plus className="w-4 h-4 mr-2" />
                      Emitir Primer Certificado
                    </ModernButton>
                  </Link>
                )}
              </div>
            </ModernCardContent>
          </ModernCard>
        )}
      </AnimateIn>
    </div>
  );
}
