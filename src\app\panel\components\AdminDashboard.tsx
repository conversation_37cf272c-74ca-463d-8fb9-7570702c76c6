"use client";

import {
  Award,
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  Calendar,
  Settings,
  Users,
  Zap,
} from "lucide-react";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import DatabaseStatus from "@/app/components/DatabaseStatus";
import { AnimateIn, StaggeredList } from "@/components/ui/animations";
import { PageError } from "@/components/ui/error-boundary";
import { LoadingPage } from "@/components/ui/loading";
import {
  ModernCard,
  ModernCardContent,
  StatCard,
} from "@/components/ui/modern-card";
import { ensureValidSession, supabase } from "@/lib/supabase";

// Tipos para mejorar la seguridad de tipos
interface DashboardStats {
  totalStudents: number;
  totalCertificates: number;
  totalCourses: number;
  totalInstructors: number;
  recentActivity: RecentActivity[];
  monthlyGrowth: {
    students: number;
    certificates: number;
    courses: number;
  };
}

interface RecentActivity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
}

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface AdminDashboardProps {
  user: User;
}

export default function AdminDashboard({ user: _user }: AdminDashboardProps) {
  const [stats, setStats] = useState<DashboardStats>({
    totalStudents: 0,
    totalCertificates: 0,
    totalCourses: 0,
    totalInstructors: 0,
    recentActivity: [],
    monthlyGrowth: {
      students: 0,
      certificates: 0,
      courses: 0,
    },
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Verificar sesión válida
      const isSessionValid = await ensureValidSession();
      if (!isSessionValid) {
        throw new Error("Sesión no válida");
      }

      // Obtener estadísticas en paralelo
      const [
        studentsResult,
        certificatesResult,
        coursesResult,
        instructorsResult,
      ] = await Promise.all([
        supabase
          .from("users")
          .select("id", { count: "exact" })
          .eq("role", "student"),
        supabase.from("certificates").select("id", { count: "exact" }),
        supabase.from("courses").select("id", { count: "exact" }),
        supabase
          .from("users")
          .select("id", { count: "exact" })
          .eq("role", "instructor"),
      ]);

      // Verificar errores
      if (studentsResult.error) throw studentsResult.error;
      if (certificatesResult.error) throw certificatesResult.error;
      if (coursesResult.error) throw coursesResult.error;
      if (instructorsResult.error) throw instructorsResult.error;

      // Obtener actividad reciente (últimos certificados emitidos)
      const { data: recentCertificates, error: recentError } = await supabase
        .from("certificates")
        .select(`
          id,
          certificate_number,
          issue_date,
          users!inner(first_name, last_name),
          courses!inner(title)
        `)
        .order("issue_date", { ascending: false })
        .limit(5);

      if (recentError) throw recentError;

      // Formatear actividad reciente
      const recentActivity: RecentActivity[] = (recentCertificates || []).map(
        (cert) => ({
          id: cert.id,
          type: "certificate",
          description: `Certificado emitido para ${cert.users?.first_name} ${cert.users?.last_name} - ${cert.courses?.title}`,
          timestamp: cert.issue_date,
        }),
      );

      // Calcular crecimiento mensual (simplificado)
      const currentDate = new Date();
      const lastMonth = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() - 1,
        1,
      );

      const [monthlyStudents, monthlyCertificates] = await Promise.all([
        supabase
          .from("users")
          .select("id", { count: "exact" })
          .eq("role", "student")
          .gte("created_at", lastMonth.toISOString()),
        supabase
          .from("certificates")
          .select("id", { count: "exact" })
          .gte("issue_date", lastMonth.toISOString()),
      ]);

      setStats({
        totalStudents: studentsResult.count || 0,
        totalCertificates: certificatesResult.count || 0,
        totalCourses: coursesResult.count || 0,
        totalInstructors: instructorsResult.count || 0,
        recentActivity,
        monthlyGrowth: {
          students: monthlyStudents.count || 0,
          certificates: monthlyCertificates.count || 0,
          courses: 0, // Simplificado por ahora
        },
      });
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      setError(error instanceof Error ? error.message : "Error desconocido");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  if (loading) {
    return <LoadingPage />;
  }

  if (error) {
    return <PageError message={error} onRetry={fetchDashboardData} />;
  }

  const quickActions = [
    {
      title: "Nuevo Alumno",
      description: "Registrar un nuevo estudiante",
      href: "/panel/alumnos/nuevo",
      icon: Users,
      color: "bg-blue-500",
    },
    {
      title: "Nuevo Certificado",
      description: "Emitir un certificado",
      href: "/panel/certificados/nuevo",
      icon: Award,
      color: "bg-green-500",
    },
    {
      title: "Nuevo Curso",
      description: "Crear un nuevo curso",
      href: "/panel/cursos/nuevo",
      icon: BookOpen,
      color: "bg-purple-500",
    },
    {
      title: "Ver Reportes",
      description: "Analizar estadísticas",
      href: "/panel/reportes",
      icon: BarChart3,
      color: "bg-orange-500",
    },
  ];

  return (
    <div className="space-y-8">
      {/* Estadísticas principales */}
      <AnimateIn animation="fade" duration="normal">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Alumnos"
            value={stats.totalStudents}
            change={`+${stats.monthlyGrowth.students} este mes`}
            trend="up"
            icon={Users}
            color="blue"
          />
          <StatCard
            title="Certificados Emitidos"
            value={stats.totalCertificates}
            change={`+${stats.monthlyGrowth.certificates} este mes`}
            trend="up"
            icon={Award}
            color="green"
          />
          <StatCard
            title="Cursos Activos"
            value={stats.totalCourses}
            change="Cursos disponibles"
            trend="neutral"
            icon={BookOpen}
            color="purple"
          />
          <StatCard
            title="Instructores"
            value={stats.totalInstructors}
            change="Personal docente"
            trend="neutral"
            icon={Users}
            color="orange"
          />
        </div>
      </AnimateIn>

      {/* Acciones rápidas */}
      <AnimateIn animation="slide" direction="up" delay={200}>
        <ModernCard>
          <ModernCardContent>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-foreground">
                Acciones Rápidas
              </h2>
              <Zap className="w-5 h-5 text-primary" />
            </div>
            <StaggeredList className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action, _index) => (
                <Link
                  key={action.title}
                  href={action.href}
                  className="group block"
                >
                  <div className="bg-muted hover:bg-muted/80 rounded-lg p-4 transition-all duration-200 hover:shadow-md border border-border hover:border-primary/20">
                    <div className="flex items-center space-x-3">
                      <div className={`${action.color} p-2 rounded-lg`}>
                        <action.icon className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                          {action.title}
                        </h3>
                        <p className="text-xs text-muted-foreground truncate">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </StaggeredList>
          </ModernCardContent>
        </ModernCard>
      </AnimateIn>

      {/* Actividad reciente y estado del sistema */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Actividad reciente */}
        <AnimateIn animation="slide" direction="left" delay={400}>
          <ModernCard>
            <ModernCardContent>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-foreground">
                  Actividad Reciente
                </h2>
                <Calendar className="w-5 h-5 text-primary" />
              </div>
              <div className="space-y-4">
                {stats.recentActivity.length > 0 ? (
                  stats.recentActivity.map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg"
                    >
                      <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-foreground">
                          {activity.description}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground text-center py-4">
                    No hay actividad reciente
                  </p>
                )}
              </div>
            </ModernCardContent>
          </ModernCard>
        </AnimateIn>

        {/* Estado del sistema */}
        <AnimateIn animation="slide" direction="right" delay={600}>
          <ModernCard>
            <ModernCardContent>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-foreground">
                  Estado del Sistema
                </h2>
                <Settings className="w-5 h-5 text-primary" />
              </div>
              <DatabaseStatus />
            </ModernCardContent>
          </ModernCard>
        </AnimateIn>
      </div>
    </div>
  );
}
