"use client";

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Plus, Search, UserPlus } from "lucide-react";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { AnimateIn, StaggeredList } from "@/components/ui/animations";
import { PageError } from "@/components/ui/error-boundary";
import { LoadingPage } from "@/components/ui/loading";
import { ModernButton } from "@/components/ui/modern-button";
import {
  ModernCard,
  ModernCardContent,
} from "@/components/ui/modern-card";

// Define types for User (representing a student)
type User = {
  id: string;
  full_name?: string; // Will be constructed
  first_name?: string; // From users table metadata or raw_user_meta_data
  last_name?: string; // From users table metadata or raw_user_meta_data
  email: string; // From auth.users
  role: string; // From users table
  created_at: string; // From auth.users
  certificate_count: number; // Calculated
  identity_document?: string; // RUT or other identity document
};

export default function StudentsPage() {
  const [students, setStudents] = useState<User[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const supabase = createClientComponentClient();

  const fetchStudents = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch students from users table with certificate count
      const { data: studentsData, error: studentsError } = await supabase
        .from("users")
        .select(`
          id,
          email,
          first_name,
          last_name,
          role,
          created_at,
          identity_document,
          certificates (count)
        `)
        .eq("role", "student")
        .order("created_at", { ascending: false });

      if (studentsError) {
        throw studentsError;
      }

      // Process the data to include full_name and certificate_count
      const processedStudents: User[] = (studentsData || []).map((student) => ({
        ...student,
        full_name: student.first_name && student.last_name 
          ? `${student.first_name} ${student.last_name}`.trim()
          : student.email,
        certificate_count: Array.isArray(student.certificates) 
          ? student.certificates.length 
          : 0,
      }));

      setStudents(processedStudents);
      setFilteredStudents(processedStudents);
    } catch (error) {
      console.error("Error fetching students:", error);
      setError(error instanceof Error ? error.message : "Error desconocido");
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    fetchStudents();
  }, [fetchStudents]);

  // Filter students based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredStudents(students);
      return;
    }

    const filtered = students.filter(student =>
      student.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.identity_document?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    setFilteredStudents(filtered);
  }, [students, searchTerm]);

  if (loading) {
    return <LoadingPage />;
  }

  if (error) {
    return <PageError message={error} onRetry={fetchStudents} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-border pb-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Gestión de Alumnos</h1>
          <p className="text-muted-foreground mt-1">
            Administra los estudiantes registrados en el sistema
          </p>
        </div>
        <Link href="/panel/alumnos/nuevo">
          <ModernButton>
            <Plus className="w-4 h-4 mr-2" />
            Nuevo Alumno
          </ModernButton>
        </Link>
      </div>

      {/* Search and Stats */}
      <AnimateIn animation="fade" duration="normal">
        <ModernCard>
          <ModernCardContent>
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Buscar por nombre, email o RUT..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
              </div>

              {/* Stats */}
              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground">{students.length}</div>
                  <div className="text-muted-foreground">Total Alumnos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground">
                    {students.reduce((sum, student) => sum + student.certificate_count, 0)}
                  </div>
                  <div className="text-muted-foreground">Certificados</div>
                </div>
              </div>
            </div>
          </ModernCardContent>
        </ModernCard>
      </AnimateIn>

      {/* Students List */}
      <AnimateIn animation="slide" direction="up" delay={200}>
        {filteredStudents.length > 0 ? (
          <StaggeredList className="space-y-4">
            {filteredStudents.map((student) => (
              <ModernCard key={student.id}>
                <ModernCardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 flex-1 min-w-0">
                      <div className="bg-primary/10 p-3 rounded-lg">
                        <UserPlus className="w-6 h-6 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-foreground truncate">
                          {student.full_name}
                        </h3>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-sm text-muted-foreground">
                            {student.email}
                          </span>
                          {student.identity_document && (
                            <span className="text-sm text-muted-foreground">
                              RUT: {student.identity_document}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-sm text-muted-foreground">
                            Registrado: {new Date(student.created_at).toLocaleDateString()}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            Certificados: {student.certificate_count}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        {student.role}
                      </span>
                      
                      <Link href={`/panel/alumnos/${student.id}`}>
                        <ModernButton variant="outline" size="sm">
                          Ver Detalles
                        </ModernButton>
                      </Link>
                    </div>
                  </div>
                </ModernCardContent>
              </ModernCard>
            ))}
          </StaggeredList>
        ) : (
          <ModernCard>
            <ModernCardContent>
              <div className="text-center py-12">
                <UserPlus className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">
                  {searchTerm ? "No se encontraron alumnos" : "No hay alumnos registrados"}
                </h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm 
                    ? "Intenta ajustar los términos de búsqueda"
                    : "Comienza registrando el primer alumno en el sistema"}
                </p>
                {!searchTerm && (
                  <Link href="/panel/alumnos/nuevo">
                    <ModernButton>
                      <Plus className="w-4 h-4 mr-2" />
                      Registrar Primer Alumno
                    </ModernButton>
                  </Link>
                )}
              </div>
            </ModernCardContent>
          </ModernCard>
        )}
      </AnimateIn>
    </div>
  );
}
