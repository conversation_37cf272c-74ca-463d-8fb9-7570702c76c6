import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { type NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  const next = requestUrl.searchParams.get("next") || "/panel-alumno";

  console.log("Auth callback: Recibido código de autenticación");

  if (code) {
    try {
      // Crear cliente Supabase
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

      // Intercambiar el código por una sesión
      const { error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error("Error al intercambiar código por sesión:", error);
        return NextResponse.redirect(
          new URL("/login?error=auth_callback_failed", requestUrl.origin),
        );
      }

      console.log("Auth callback: Sesión establecida correctamente");

      // Obtener sesión e información del usuario
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (session) {
        try {
          // Verificar rol del usuario (opcional, solo para logging)
          const { data: profile } = await supabase
            .from("users")
            .select("role")
            .eq("id", session.user.id)
            .single();

          console.log("Auth callback: Perfil de usuario:", profile);

          // Determinar la ruta de redirección - todos van al panel unificado
          let redirectUrl = "/panel";

          // Si hay un parámetro next en la URL, usarlo como redirección
          if (next?.startsWith("/panel")) {
            redirectUrl = next;
          }

          console.log("Auth callback: Redirigiendo a", redirectUrl);

          // Redirigir al usuario
          return NextResponse.redirect(new URL(redirectUrl, requestUrl.origin));
        } catch (error) {
          console.error("Error en callback de autenticación:", error);
          // Si hay un error, redirigir al panel de alumno por defecto
          return NextResponse.redirect(
            new URL("/panel-alumno", requestUrl.origin),
          );
        }
      } else {
        console.error(
          "Auth callback: No se pudo establecer la sesión después del intercambio",
        );
        return NextResponse.redirect(
          new URL("/login?error=session_not_found", requestUrl.origin),
        );
      }
    } catch (error) {
      console.error("Error general en callback de autenticación:", error);
      return NextResponse.redirect(
        new URL("/login?error=auth_exception", requestUrl.origin),
      );
    }
  }

  // Si no hay código, redirigir a la página de inicio
  console.warn("Auth callback: No se proporcionó código de autenticación");
  return NextResponse.redirect(new URL("/", request.url));
}
