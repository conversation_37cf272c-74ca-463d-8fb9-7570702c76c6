/**
 * @fileoverview Dashboard Metrics Components
 *
 * Componentes para mostrar métricas y estadísticas en dashboards de QR CURSE.
 * Incluye tarjetas de métricas, gráficos y indicadores de rendimiento.
 */

"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Award,
  BookOpen,
  CheckCircle,
  Clock,
  Minus,
  TrendingDown,
  TrendingUp,
  Users,
  XCircle,
} from "lucide-react";
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ModernCard,
  StatCard,
  ModernCardContent,
  ModernCardHeader,
  ModernCardTitle,
} from "@/components/ui/modern-card";
import { cn } from "@/lib/utils";

interface MetricData {
  id: string;
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon?: React.ComponentType<{ className?: string }>;
  trend?: "up" | "down" | "neutral";
  color?: "default" | "success" | "warning" | "destructive";
  description?: string;
}

interface MetricsGridProps {
  metrics: MetricData[];
  isLoading?: boolean;
  className?: string;
}

/**
 * Grid de métricas principales del dashboard
 */
export function MetricsGrid({
  metrics,
  isLoading = false,
  className,
}: MetricsGridProps) {
  if (isLoading) {
    return (
      <div
        className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}
      >
        {Array.from({ length: 4 }).map((_, i) => (
          <MetricCardSkeleton key={i} />
        ))}
      </div>
    );
  }

  return (
    <div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}>
      {metrics.map((metric) => (
        <MetricCard key={metric.id} metric={metric} />
      ))}
    </div>
  );
}

/**
 * Tarjeta individual de métrica
 */
export function MetricCard({ metric }: { metric: MetricData }) {
  const Icon = metric.icon || Award;

  return (
    <StatCard
      title={metric.title}
      value={
        typeof metric.value === "number"
          ? metric.value.toLocaleString()
          : metric.value
      }
      description={metric.description}
      icon={Icon}
      trend={metric.trend}
      trendValue={
        metric.change
          ? `${metric.change > 0 ? "+" : ""}${metric.change}%`
          : undefined
      }
      variant="glass"
    />
  );
}

/**
 * Tarjeta de métrica clásica (para compatibilidad)
 */
export function ClassicMetricCard({ metric }: { metric: MetricData }) {
  const Icon = metric.icon || Award;
  const TrendIcon = getTrendIcon(metric.trend);
  const trendColor = getTrendColor(metric.trend);

  return (
    <ModernCard variant="default" interactive glowEffect>
      <ModernCardHeader>
        <div className="flex flex-row items-center justify-between space-y-0">
          <ModernCardTitle className="text-sm font-medium text-muted-foreground">
            {metric.title}
          </ModernCardTitle>
          <div className="p-2 rounded-xl bg-primary/10 transition-all duration-200 group-hover:scale-110">
            <Icon
              className={cn(
                "h-5 w-5",
                metric.color === "success" && "text-success",
                metric.color === "warning" && "text-warning",
                metric.color === "destructive" && "text-destructive",
                !metric.color && "text-primary",
              )}
            />
          </div>
        </div>
      </ModernCardHeader>
      <ModernCardContent>
        <div className="text-3xl font-bold mb-2">
          {typeof metric.value === "number"
            ? metric.value.toLocaleString()
            : metric.value}
        </div>

        {metric.change !== undefined && (
          <div className="flex items-center space-x-2 text-sm">
            <TrendIcon className={cn("h-4 w-4", trendColor)} />
            <span className={cn("font-medium", trendColor)}>
              {metric.change > 0 ? "+" : ""}
              {metric.change}%
            </span>
            <span className="text-muted-foreground">
              {metric.changeLabel || "desde el mes pasado"}
            </span>
          </div>
        )}

        {metric.description && (
          <p className="text-sm text-muted-foreground mt-2 leading-relaxed">
            {metric.description}
          </p>
        )}
      </ModernCardContent>
    </ModernCard>
  );
}

/**
 * Skeleton para tarjeta de métrica
 */
function MetricCardSkeleton() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-4 rounded" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-16 mb-2" />
        <Skeleton className="h-3 w-32" />
      </CardContent>
    </Card>
  );
}

/**
 * Métricas específicas para certificados
 */
export function CertificateMetrics({
  data,
  isLoading = false,
}: {
  data?: {
    total: number;
    active: number;
    revoked: number;
    thisMonth: number;
    changePercent: number;
  };
  isLoading?: boolean;
}) {
  if (isLoading || !data) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <MetricCardSkeleton key={i} />
        ))}
      </div>
    );
  }

  const metrics: MetricData[] = [
    {
      id: "total-certificates",
      title: "Total Certificados",
      value: data.total,
      icon: Award,
      color: "default",
      description: "Certificados emitidos en total",
    },
    {
      id: "active-certificates",
      title: "Certificados Activos",
      value: data.active,
      icon: CheckCircle,
      color: "success",
      description: "Certificados válidos y activos",
    },
    {
      id: "revoked-certificates",
      title: "Certificados Revocados",
      value: data.revoked,
      icon: XCircle,
      color: "destructive",
      description: "Certificados revocados o cancelados",
    },
    {
      id: "monthly-certificates",
      title: "Este Mes",
      value: data.thisMonth,
      change: data.changePercent,
      trend:
        data.changePercent > 0
          ? "up"
          : data.changePercent < 0
            ? "down"
            : "neutral",
      icon: Clock,
      color: "default",
      description: "Certificados emitidos este mes",
    },
  ];

  return <MetricsGrid metrics={metrics} />;
}

/**
 * Métricas específicas para usuarios
 */
export function UserMetrics({
  data,
  isLoading = false,
}: {
  data?: {
    total: number;
    students: number;
    instructors: number;
    active: number;
    changePercent: number;
  };
  isLoading?: boolean;
}) {
  if (isLoading || !data) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <MetricCardSkeleton key={i} />
        ))}
      </div>
    );
  }

  const metrics: MetricData[] = [
    {
      id: "total-users",
      title: "Total Usuarios",
      value: data.total,
      change: data.changePercent,
      trend:
        data.changePercent > 0
          ? "up"
          : data.changePercent < 0
            ? "down"
            : "neutral",
      icon: Users,
      color: "default",
    },
    {
      id: "students",
      title: "Estudiantes",
      value: data.students,
      icon: Users,
      color: "default",
    },
    {
      id: "instructors",
      title: "Instructores",
      value: data.instructors,
      icon: Users,
      color: "default",
    },
    {
      id: "active-users",
      title: "Usuarios Activos",
      value: data.active,
      icon: CheckCircle,
      color: "success",
    },
  ];

  return <MetricsGrid metrics={metrics} />;
}

/**
 * Indicador de estado del sistema
 */
export function SystemStatus({
  status = "operational",
  lastUpdate,
}: {
  status?: "operational" | "degraded" | "down";
  lastUpdate?: string;
}) {
  const statusConfig = {
    operational: {
      label: "Operacional",
      color: "success" as const,
      icon: CheckCircle,
      description: "Todos los sistemas funcionan correctamente",
    },
    degraded: {
      label: "Degradado",
      color: "warning" as const,
      icon: AlertTriangle,
      description: "Algunos servicios pueden estar lentos",
    },
    down: {
      label: "Fuera de Servicio",
      color: "destructive" as const,
      icon: XCircle,
      description: "Servicios no disponibles",
    },
  };

  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">
          Estado del Sistema
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2">
          <Icon
            className={cn(
              "h-4 w-4",
              config.color === "success" && "text-green-600",
              config.color === "warning" && "text-yellow-600",
              config.color === "destructive" && "text-red-600",
            )}
          />
          <Badge
            variant={config.color === "success" ? "default" : config.color}
          >
            {config.label}
          </Badge>
        </div>
        <p className="text-xs text-muted-foreground mt-2">
          {config.description}
        </p>
        {lastUpdate && (
          <p className="text-xs text-muted-foreground mt-1">
            Última actualización: {lastUpdate}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Resumen de actividad reciente
 */
export function ActivitySummary({
  activities = [],
  isLoading = false,
}: {
  activities?: Array<{
    id: string;
    type: "certificate" | "user" | "course";
    action: string;
    timestamp: string;
    user?: string;
  }>;
  isLoading?: boolean;
}) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Actividad Reciente</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-1 flex-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Actividad Reciente</CardTitle>
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-4">
            No hay actividad reciente
          </p>
        ) : (
          <div className="space-y-3">
            {activities.slice(0, 5).map((activity) => {
              const Icon = getActivityIcon(activity.type);
              return (
                <div key={activity.id} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <Icon className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {activity.action}
                    </p>
                    <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                      <span>{activity.timestamp}</span>
                      {activity.user && (
                        <>
                          <span>•</span>
                          <span>{activity.user}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Funciones auxiliares
function getTrendIcon(trend?: "up" | "down" | "neutral") {
  switch (trend) {
    case "up":
      return TrendingUp;
    case "down":
      return TrendingDown;
    default:
      return Minus;
  }
}

function getTrendColor(trend?: "up" | "down" | "neutral") {
  switch (trend) {
    case "up":
      return "text-green-600";
    case "down":
      return "text-red-600";
    default:
      return "text-muted-foreground";
  }
}

function getActivityIcon(type: "certificate" | "user" | "course") {
  switch (type) {
    case "certificate":
      return Award;
    case "user":
      return Users;
    case "course":
      return BookOpen;
    default:
      return Award;
  }
}
